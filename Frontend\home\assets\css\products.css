/*--------------------------------------------------------------
# Products Page Styles
--------------------------------------------------------------*/

/* Products Hero Section */
.products-hero {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 140px 0 80px 0;
  position: relative;
}

.products-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59,130,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.products-hero .container {
  position: relative;
  z-index: 1;
}

.products-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 20px;
  line-height: 1.2;
}

.products-hero p {
  font-size: 1.2rem;
  color: var(--default-color);
  margin-bottom: 40px;
  opacity: 0.8;
}

/* Hero Stats */
.hero-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-top: 40px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--accent-color);
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--default-color);
  opacity: 0.7;
  font-weight: 500;
}

/* Platform Filter Section */
.platform-filter-section {
  background: #ffffff;
  padding: 40px 0;
  border-bottom: 1px solid rgba(0, 119, 255, 0.1);
}

.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-tab {
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 119, 255, 0.1);
  color: var(--default-color);
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-tab:hover {
  background: rgba(0, 119, 255, 0.1);
  border-color: var(--accent-color);
  color: var(--accent-color);
  transform: translateY(-2px);
}

.filter-tab.active {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.filter-tab i {
  margin-right: 8px;
  font-size: 16px;
}

/* Products Grid Section */
.products-grid-section {
  background: #ffffff;
  padding: 60px 0;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 32px;
}

/* Product Card */
.product-card {
  background: #ffffff;
  border: 1px solid rgba(0, 119, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.product-card:hover::before {
  transform: scaleX(1);
}

.product-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color);
}

/* Product Header */
.product-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.product-logo {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: white;
  margin-right: 16px;
  position: relative;
  overflow: hidden;
}

.product-logo::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.product-card:hover .product-logo::before {
  animation: shine 0.6s ease-in-out;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}

.product-info h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 4px;
  line-height: 1.3;
}

.product-info .product-category {
  font-size: 14px;
  color: var(--accent-color);
  font-weight: 500;
}

/* Product Description */
.product-description {
  color: var(--default-color);
  opacity: 0.8;
  margin-bottom: 24px;
  line-height: 1.6;
  flex-grow: 1;
}

/* Product Features */
.product-features {
  margin-bottom: 24px;
}

.product-features h4 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 12px;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--default-color);
}

.feature-item i {
  color: var(--accent-color);
  margin-right: 8px;
  font-size: 16px;
}

/* Product Stats */
.product-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
}

.stat {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--accent-color);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--default-color);
  opacity: 0.7;
}

/* Product Actions */
.product-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.action-btn.primary {
  background: var(--accent-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 119, 255, 0.3);
  color: white;
}

.action-btn.secondary {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 119, 255, 0.1);
  color: var(--default-color);
}

.action-btn.secondary:hover {
  background: rgba(0, 119, 255, 0.1);
  border-color: var(--accent-color);
  color: var(--accent-color);
}

/* Features Comparison Section */
.features-comparison-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 80px 0;
  position: relative;
}

.features-comparison-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.features-comparison-section .container {
  position: relative;
  z-index: 1;
}

.section-header {
  text-align: center;
  margin-bottom: 48px;
}

.section-header h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 12px;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--default-color);
  opacity: 0.8;
  margin: 0;
}

/* Comparison Table */
.comparison-table-wrapper {
  background: #ffffff;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-x: auto;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.comparison-table th,
.comparison-table td {
  padding: 16px 20px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 119, 255, 0.1);
}

.comparison-table th {
  background: linear-gradient(
    135deg,
    rgba(0, 119, 255, 0.1),
    rgba(0, 119, 255, 0.05)
  );
  font-weight: 600;
  color: var(--heading-color);
  font-size: 14px;
}

.comparison-table th:first-child {
  text-align: left;
  border-radius: 12px 0 0 0;
}

.comparison-table th:last-child {
  border-radius: 0 12px 0 0;
}

.comparison-table td {
  font-size: 14px;
  color: var(--default-color);
}

.comparison-table td:first-child {
  text-align: left;
  font-weight: 500;
}

.comparison-table i {
  font-size: 18px;
}

.comparison-table tr:hover {
  background: rgba(248, 250, 252, 0.5);
}

/* Integration Guide Section */
.integration-guide-section {
  background: #ffffff;
  padding: 80px 0;
}

.guide-step {
  text-align: center;
  position: relative;
  padding: 32px 24px;
  height: 100%;
}

.step-number {
  position: absolute;
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 32px;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.step-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  color: white;
  font-size: 32px;
}

.guide-step h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 16px;
}

.guide-step p {
  color: var(--default-color);
  opacity: 0.8;
  line-height: 1.6;
  margin: 0;
}

/* Platform Colors */
.taobao {
  background: linear-gradient(135deg, #ff6900, #ff4500);
}
.tmall {
  background: linear-gradient(135deg, #ff0036, #d50000);
}
.jd {
  background: linear-gradient(135deg, #e1251b, #c62828);
}
.pdd {
  background: linear-gradient(135deg, #e02020, #b71c1c);
}
.douyin {
  background: linear-gradient(135deg, #000000, #333333);
}
.kuaishou {
  background: linear-gradient(135deg, #ff6900, #ff5722);
}
.xianyu {
  background: linear-gradient(135deg, #ffb300, #ff8f00);
}
.xiaohongshu {
  background: linear-gradient(135deg, #ff2442, #e91e63);
}
.weidian {
  background: linear-gradient(135deg, #07c160, #4caf50);
}
.youzan {
  background: linear-gradient(135deg, #06ae56, #388e3c);
}

/* Popular Badge */
.popular-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* Product Card Filtering */
.product-card.hidden {
  display: none;
}

.product-card.show {
  display: flex;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
  }

  .hero-stats {
    gap: 40px;
  }

  .filter-tabs {
    gap: 12px;
  }

  .filter-tab {
    padding: 10px 16px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .products-hero {
    padding: 120px 0 60px 0;
  }

  .products-hero h1 {
    font-size: 2.5rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 24px;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .filter-tabs {
    flex-direction: column;
    align-items: center;
  }

  .filter-tab {
    width: 200px;
    justify-content: center;
  }

  .comparison-table-wrapper {
    padding: 20px;
  }

  .comparison-table th,
  .comparison-table td {
    padding: 12px 8px;
    font-size: 12px;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .product-stats {
    flex-direction: column;
    gap: 12px;
  }

  .product-actions {
    flex-direction: column;
  }
}
