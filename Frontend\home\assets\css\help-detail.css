/*--------------------------------------------------------------
# Help Detail Page Styles
--------------------------------------------------------------*/

/* Breadcrumb Section */
.breadcrumb-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 120px 0 40px 0;
  position: relative;
}

.breadcrumb-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59,130,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.breadcrumb-section .container {
  position: relative;
  z-index: 1;
}

.breadcrumb {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px 24px;
  margin: 0;
  border: 1px solid rgba(0, 119, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.breadcrumb-item {
  font-size: 14px;
  font-weight: 500;
}

.breadcrumb-item a {
  color: var(--accent-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
  color: var(--accent-hover);
}

.breadcrumb-item.active {
  color: var(--default-color);
  opacity: 0.8;
}

.breadcrumb-item i {
  margin-right: 6px;
}

/* Document Detail Section */
.document-detail-section {
  padding: 40px 0 80px 0;
  background: #ffffff;
}

/* Detail Sidebar */
.detail-sidebar {
  position: sticky;
  top: 120px;
}

.sidebar-header {
  margin-bottom: 32px;
}

.back-to-help {
  display: flex;
  align-items: center;
  background: rgba(0, 119, 255, 0.1);
  border: 1px solid rgba(0, 119, 255, 0.2);
  color: var(--accent-color);
  padding: 12px 20px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  width: 100%;
  justify-content: center;
}

.back-to-help:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 119, 255, 0.3);
}

.back-to-help i {
  margin-right: 8px;
}

/* Document Navigation */
.document-nav {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid rgba(0, 119, 255, 0.1);
}

.document-nav h5 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--accent-color);
}

.nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-list li {
  margin-bottom: 8px;
}

.nav-list a {
  display: block;
  padding: 8px 12px;
  color: var(--default-color);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 14px;
  line-height: 1.4;
}

.nav-list a:hover {
  background: rgba(0, 119, 255, 0.1);
  color: var(--accent-color);
  transform: translateX(4px);
}

.nav-list a.active {
  background: var(--accent-color);
  color: white;
}

/* Help Contact */
.help-contact {
  background: linear-gradient(135deg, rgba(0, 119, 255, 0.1), rgba(0, 119, 255, 0.05));
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  border: 1px solid rgba(0, 119, 255, 0.2);
}

.help-contact h6 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 12px;
}

.help-contact p {
  font-size: 14px;
  color: var(--default-color);
  opacity: 0.8;
  margin-bottom: 16px;
  line-height: 1.5;
}

.contact-btn {
  display: inline-flex;
  align-items: center;
  background: var(--accent-color);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;
}

.contact-btn:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 119, 255, 0.3);
  color: white;
}

.contact-btn i {
  margin-right: 8px;
}

/* Document Article */
.document-article {
  background: #ffffff;
}

/* Article Header */
.article-header {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 2px solid rgba(0, 119, 255, 0.1);
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
}

.category-badge {
  background: var(--accent-color);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reading-time,
.update-time {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--default-color);
  opacity: 0.7;
}

.reading-time i,
.update-time i {
  margin-right: 6px;
  font-size: 16px;
}

.article-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 16px;
  line-height: 1.2;
}

.article-description p {
  font-size: 1.2rem;
  color: var(--default-color);
  opacity: 0.8;
  margin: 0;
  line-height: 1.6;
}

.article-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.action-btn {
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 119, 255, 0.1);
  color: var(--default-color);
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-btn:hover {
  background: rgba(0, 119, 255, 0.1);
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.action-btn i {
  margin-right: 6px;
}

.action-btn.bookmarked {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

/* Table of Contents */
.table-of-contents {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 119, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 40px;
}

.table-of-contents h6 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.table-of-contents h6 i {
  margin-right: 8px;
  color: var(--accent-color);
}

.table-of-contents ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.table-of-contents li {
  margin-bottom: 8px;
}

.table-of-contents a {
  color: var(--default-color);
  text-decoration: none;
  font-size: 14px;
  line-height: 1.5;
  transition: all 0.3s ease;
  display: block;
  padding: 4px 0;
}

.table-of-contents a:hover {
  color: var(--accent-color);
  padding-left: 8px;
}

/* Article Content */
.article-content {
  font-size: 16px;
  line-height: 1.8;
  color: var(--default-color);
  margin-bottom: 48px;
}

.article-content h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 40px 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(0, 119, 255, 0.1);
  position: relative;
}

.article-content h2::before {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--accent-color);
}

.article-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 32px 0 16px 0;
}

.article-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 24px 0 12px 0;
}

.article-content p {
  margin-bottom: 20px;
}

.article-content ul,
.article-content ol {
  margin-bottom: 20px;
  padding-left: 28px;
}

.article-content li {
  margin-bottom: 8px;
}

.article-content code {
  background: rgba(0, 119, 255, 0.1);
  color: var(--accent-color);
  padding: 3px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  font-weight: 500;
}

.article-content pre {
  background: #f8fafc;
  border: 1px solid rgba(0, 119, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  overflow-x: auto;
  margin: 24px 0;
  position: relative;
}

.article-content pre code {
  background: none;
  color: var(--default-color);
  padding: 0;
  font-size: 14px;
}

.article-content blockquote {
  background: rgba(0, 119, 255, 0.05);
  border-left: 4px solid var(--accent-color);
  padding: 20px 24px;
  margin: 24px 0;
  border-radius: 0 8px 8px 0;
  font-style: italic;
}

/* Article Footer */
.article-footer {
  border-top: 2px solid rgba(0, 119, 255, 0.1);
  padding-top: 32px;
}

/* Feedback Section */
.feedback-section {
  text-align: center;
  margin-bottom: 40px;
}

.feedback-section h6 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 20px;
}

.feedback-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.feedback-btn {
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 119, 255, 0.1);
  color: var(--default-color);
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.feedback-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feedback-btn.helpful:hover {
  background: #10b981;
  border-color: #10b981;
  color: white;
}

.feedback-btn.not-helpful:hover {
  background: #ef4444;
  border-color: #ef4444;
  color: white;
}

.feedback-btn i {
  margin-right: 8px;
}

/* Document Navigation */
.document-navigation {
  display: flex;
  justify-content: space-between;
  gap: 24px;
}

.nav-item {
  flex: 1;
  max-width: 45%;
}

.nav-item.prev {
  text-align: left;
}

.nav-item.next {
  text-align: right;
}

.nav-label {
  display: block;
  font-size: 12px;
  color: var(--default-color);
  opacity: 0.6;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-link {
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 119, 255, 0.1);
  padding: 16px 20px;
  border-radius: 12px;
  text-decoration: none;
  color: var(--default-color);
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(0, 119, 255, 0.1);
  border-color: var(--accent-color);
  color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-item.prev .nav-link {
  justify-content: flex-start;
}

.nav-item.next .nav-link {
  justify-content: flex-end;
}

.nav-link i {
  font-size: 18px;
}

.nav-item.prev .nav-link i {
  margin-right: 12px;
}

.nav-item.next .nav-link i {
  margin-left: 12px;
}

.nav-title {
  font-weight: 500;
  line-height: 1.4;
}

/* Related Documents Section */
.related-documents-section {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 80px 0;
  position: relative;
}

.related-documents-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.related-documents-section .container {
  position: relative;
  z-index: 1;
}

.section-header {
  text-align: center;
  margin-bottom: 48px;
}

.section-header h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 12px;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--default-color);
  opacity: 0.8;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .detail-sidebar {
    position: static;
    margin-bottom: 40px;
  }
  
  .document-navigation {
    flex-direction: column;
  }
  
  .nav-item {
    max-width: 100%;
  }
  
  .feedback-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .feedback-btn {
    width: 200px;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .breadcrumb-section {
    padding: 100px 0 30px 0;
  }
  
  .article-title {
    font-size: 2rem;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .article-actions {
    flex-wrap: wrap;
  }
  
  .action-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }
}
