/**
 * Products Page JavaScript
 */

// 当前筛选类型
let currentFilter = 'all';

// 初始化
document.addEventListener('DOMContentLoaded', function() {
  // 初始化AOS动画
  AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    mirror: false
  });

  // 绑定筛选事件
  bindFilterEvents();
});

// 绑定筛选事件
function bindFilterEvents() {
  const filterTabs = document.querySelectorAll('.filter-tab');
  filterTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      const filter = this.dataset.filter;
      if (filter !== currentFilter) {
        // 更新选中状态
        filterTabs.forEach(t => t.classList.remove('active'));
        this.classList.add('active');
        
        // 应用筛选
        currentFilter = filter;
        filterProducts(filter);
      }
    });
  });
}



// 筛选产品
function filterProducts(filter) {
  const productCards = document.querySelectorAll('.product-card');
  
  productCards.forEach(card => {
    const category = card.dataset.category;
    const shouldShow = filter === 'all' || category === filter;
    
    if (shouldShow) {
      card.classList.remove('hidden');
      card.classList.add('show');
    } else {
      card.classList.add('hidden');
      card.classList.remove('show');
    }
  });
}

// 显示产品演示
function showProductDemo(productId) {
  // 根据产品ID显示对应的演示信息
  const productNames = {
    'taobao': '淘宝',
    'tmall': '天猫',
    'jd': '京东',
    'pdd': '拼多多',
    'douyin': '抖音',
    'kuaishou': '快手'
  };

  const productName = productNames[productId] || '该平台';
  alert(`${productName} 功能演示\n\n即将为您展示 ${productName} 平台的核心功能和操作流程。\n\n主要功能包括：\n• 自动发货\n• 订单同步\n• 库存管理\n• 数据统计\n• 批量操作`);
}


