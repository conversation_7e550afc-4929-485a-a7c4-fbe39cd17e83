/* 让 .icon-left 内的 .icon 垂直居中 */
.icon-left {
  display: flex;
  align-items: center;
  height: 100%;
  margin-right: 20px;
}
/**
* Template Name: Anyar
* Updated: Apr 28 2025 
* Author: BootstrapMade.com (optimized)
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# Font & Color Variables
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  --heading-font: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Aria<PERSON>, sans-serif;
  --nav-font: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
}

/* Global Colors - Modern Design System */
:root {
  --background-color: #ffffff;
  --default-color: #334155;
  --heading-color: #1e293b;
  --accent-color: #3b82f6;
  --accent-hover: #2563eb;
  --surface-color: #ffffff;
  --contrast-color: #ffffff;
  --border-color: #e2e8f0;
  --muted-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
}

/* Nav Menu Colors */
:root {
  --nav-color: rgba(51, 65, 85, 0.8);
  --nav-hover-color: #1e293b;
  --nav-mobile-background-color: rgba(255, 255, 255, 0.98);
  --nav-dropdown-background-color: #ffffff;
  --nav-dropdown-color: #334155;
  --nav-dropdown-hover-color: #3b82f6;
}

/* Color Presets */
.light-background {
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --border-color: #e2e8f0;
}

.dark-background {
  --background-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: rgba(255, 255, 255, 0.1);
  --contrast-color: #ffffff;
  --border-color: rgba(255, 255, 255, 0.2);
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
  font-size: 16px;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--accent-hover);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 1rem;
}

h1 {
  font-size: 2.5rem;
}
h2 {
  font-size: 2rem;
}
h3 {
  font-size: 1.5rem;
}
h4 {
  font-size: 1.25rem;
}
h5 {
  font-size: 1.125rem;
}
h6 {
  font-size: 1rem;
}

p {
  margin-bottom: 1.5rem;
  color: var(--default-color);
  line-height: 1.7;
}

.text-muted {
  color: var(--muted-color) !important;
}

/* PHP Email Form Messages */
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Pulsating Play Button */
.pulsating-play-btn {
  width: 94px;
  height: 94px;
  background: radial-gradient(
    var(--accent-color) 50%,
    color-mix(in srgb, var(--accent-color), transparent 75%) 52%
  );
  border-radius: 50%;
  display: block;
  position: relative;
  overflow: hidden;
}

.pulsating-play-btn:before {
  content: "";
  position: absolute;
  width: 120px;
  height: 120px;
  animation-delay: 0s;
  animation: pulsate-play-btn 2s;
  animation-direction: forwards;
  animation-iteration-count: infinite;
  animation-timing-function: steps;
  opacity: 1;
  border-radius: 50%;
  border: 5px solid color-mix(in srgb, var(--accent-color), transparent 30%);
  top: -15%;
  left: -15%;
  background: rgba(198, 16, 0, 0);
}

.pulsating-play-btn:after {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-40%) translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #fff;
  z-index: 100;
  transition: all 400ms cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.pulsating-play-btn:hover:before {
  content: "";
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-40%) translateY(-50%);
  width: 0;
  height: 0;
  border: none;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #fff;
  z-index: 200;
  animation: none;
  border-radius: 0;
}

.pulsating-play-btn:hover:after {
  border-left: 15px solid var(--accent-color);
  transform: scale(20);
}

@keyframes pulsate-play-btn {
  0% {
    transform: scale(0.6, 0.6);
    opacity: 1;
  }

  100% {
    transform: scale(1, 1);
    opacity: 0;
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  --background-color: rgba(255, 255, 255, 0.95);
  --default-color: #333333;
  --heading-color: #333333;
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 15px 0;
  transition: all 0.3s ease;
  position: relative;
  z-index: 997;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header .logo {
  line-height: 1;
  display: flex;
  align-items: center;
}

.header .logo svg {
  height: 32px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.header .logo svg g {
  fill: #0077ff;
}

.header .logo h1 {
  font-size: 28px;
  margin: 0;
  font-weight: 600;
  color: var(--heading-color);
}

.header .header-social-links {
  padding-right: 0;
}

.header .header-social-links .btn-login {
  display: inline-block;
  padding: 10px 24px;
  background: linear-gradient(135deg, #0077ff, #0066dd);
  color: #ffffff;
  border-radius: 25px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  box-shadow: 0 2px 10px rgba(0, 119, 255, 0.3);
}

.header .header-social-links .btn-login:hover {
  background: linear-gradient(135deg, #0066dd, #0055cc);
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 119, 255, 0.4);
  color: #ffffff;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .header {
    padding: 12px 0;
  }

  .header .container {
    padding: 0 20px;
  }

  .header .logo {
    order: 1;
    flex: 1;
  }

  .header .logo svg {
    height: 28px;
  }

  .header .header-social-links {
    order: 2;
    margin-right: 15px;
  }

  .header .navmenu {
    order: 3;
  }

  .header .header-social-links .btn-login {
    padding: 8px 20px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 10px 0;
  }

  .header .container {
    padding: 0 15px;
  }

  .header .logo svg {
    height: 26px;
  }

  .header .header-social-links .btn-login {
    padding: 6px 16px;
    font-size: 12px;
  }
}

.scrolled .header {
  --background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  padding: 10px 0;
}

/* Dark theme for hero section */
.dark-background .header {
  --background-color: rgba(0, 119, 255, 0.95);
  --default-color: #ffffff;
  --heading-color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-background .header .logo svg g {
  fill: #ffffff;
}

.dark-background .header .header-social-links .btn-login {
  background: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: none;
}

.dark-background .header .header-social-links .btn-login:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
/* Navmenu - Desktop */
@media (min-width: 1200px) {
  .navmenu {
    padding: 0;
  }

  .navmenu ul {
    margin: 0;
    padding: 0;
    display: flex;
    list-style: none;
    align-items: center;
    gap: 5px;
  }

  .navmenu li {
    position: relative;
  }

  .navmenu a,
  .navmenu a:focus {
    color: var(--nav-color);
    padding: 12px 18px;
    font-size: 15px;
    font-family: var(--nav-font);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    transition: all 0.3s ease;
    border-radius: 8px;
    position: relative;
  }

  .navmenu a::before {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    transition: 0.3s;
  }

  .navmenu li:last-child a {
    padding-right: 18px;
  }

  .navmenu li:hover > a,
  .navmenu .active,
  .navmenu .active:focus {
    color: var(--nav-hover-color);
    background: rgba(0, 119, 255, 0.1);
  }

  .navmenu li:hover > a::before,
  .navmenu .active::before,
  .navmenu .active:focus::before {
    width: 60%;
  }

  .navmenu .dropdown ul {
    margin: 0;
    padding: 10px 0;
    background: var(--nav-dropdown-background-color);
    display: block;
    position: absolute;
    visibility: hidden;
    left: 14px;
    top: 130%;
    opacity: 0;
    transition: 0.3s;
    border-radius: 4px;
    z-index: 99;
    box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.1);
  }

  .navmenu .dropdown ul li {
    min-width: 200px;
  }

  .navmenu .dropdown ul a {
    padding: 10px 20px;
    font-size: 15px;
    text-transform: none;
    color: var(--nav-dropdown-color);
  }

  .navmenu .dropdown ul a i {
    font-size: 12px;
  }

  .navmenu .dropdown ul a:hover,
  .navmenu .dropdown ul .active:hover,
  .navmenu .dropdown ul li:hover > a {
    color: var(--nav-dropdown-hover-color);
  }

  .navmenu .dropdown:hover > ul {
    opacity: 1;
    top: 100%;
    visibility: visible;
  }

  .navmenu .dropdown .dropdown ul {
    top: 0;
    left: -90%;
    visibility: hidden;
  }

  .navmenu .dropdown .dropdown:hover > ul {
    opacity: 1;
    top: 0;
    left: -100%;
    visibility: visible;
  }
}

/* Navmenu - Mobile */
@media (max-width: 1199px) {
  .mobile-nav-toggle {
    color: var(--nav-color);
    font-size: 26px;
    line-height: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 12px;
    border-radius: 12px;
    background: linear-gradient(
      135deg,
      rgba(0, 119, 255, 0.1),
      rgba(0, 119, 255, 0.05)
    );
    border: 1px solid rgba(0, 119, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 119, 255, 0.1);
    width: 48px;
    height: 48px;
    position: relative;
    overflow: hidden;
    gap: 4px;
  }

  .mobile-nav-toggle span {
    display: block;
    width: 20px;
    height: 2px;
    background: var(--nav-color);
    border-radius: 1px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-nav-toggle::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s;
  }

  .mobile-nav-toggle:hover {
    background: linear-gradient(
      135deg,
      rgba(0, 119, 255, 0.2),
      rgba(0, 119, 255, 0.1)
    );
    transform: scale(1.05) rotate(5deg);
    box-shadow: 0 4px 16px rgba(0, 119, 255, 0.2);
    border-color: rgba(0, 119, 255, 0.3);
  }

  .mobile-nav-toggle:hover::before {
    left: 100%;
  }

  .mobile-nav-toggle:active {
    transform: scale(0.95);
  }

  .navmenu {
    padding: 0;
    z-index: 9999999 !important;
  }

  .navmenu ul {
    display: none;
    list-style: none;
    position: absolute;
    inset: 80px 20px 30px 20px;
    padding: 30px 0;
    margin: 0;
    border-radius: 20px;
    background: linear-gradient(
      145deg,
      rgba(255, 255, 255, 0.95),
      rgba(255, 255, 255, 0.9)
    );
    overflow-y: auto;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999999 !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: translateY(-10px);
    opacity: 0;
  }

  .navmenu a,
  .navmenu a:focus {
    color: #2c3e50;
    padding: 18px 30px;
    font-family: var(--nav-font);
    font-size: 17px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    white-space: nowrap;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 16px;
    margin: 4px 20px;
    position: relative;
    text-decoration: none;
    min-height: 56px;
    background: transparent;
    overflow: hidden;
  }

  .navmenu a::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-50%);
    border-radius: 3px;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
  }

  .navmenu a::after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.05),
      rgba(96, 165, 250, 0.05)
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
  }

  .navmenu a i,
  .navmenu a:focus i {
    font-size: 12px;
    line-height: 0;
    margin-left: 5px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    background-color: rgba(0, 119, 255, 0.1);
  }

  .navmenu a i:hover,
  .navmenu a:focus i:hover {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: scale(1.1);
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus {
    color: #1e40af;
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.1),
      rgba(96, 165, 250, 0.08)
    );
    transform: translateX(8px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  }

  .navmenu a:hover::before,
  .navmenu .active::before,
  .navmenu .active:focus::before {
    width: 6px;
  }

  .navmenu a:hover::after,
  .navmenu .active::after,
  .navmenu .active:focus::after {
    opacity: 1;
  }

  .navmenu a:active {
    transform: translateX(4px) scale(0.98);
  }

  .navmenu .active i,
  .navmenu .active:focus i {
    background-color: var(--accent-color);
    color: var(--contrast-color);
    transform: rotate(180deg);
  }

  .navmenu .dropdown ul {
    position: static;
    display: none;
    z-index: 99;
    padding: 10px 0;
    margin: 10px 20px;
    background-color: var(--nav-dropdown-background-color);
    border: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    box-shadow: none;
    transition: all 0.5s ease-in-out;
  }

  .navmenu .dropdown ul ul {
    background-color: rgba(33, 37, 41, 0.1);
  }

  .navmenu .dropdown > .dropdown-active {
    display: block;
    background-color: rgba(33, 37, 41, 0.03);
  }

  .mobile-nav-active {
    overflow: hidden;
  }

  .mobile-nav-active .mobile-nav-toggle {
    color: #fff;
    position: fixed !important;
    font-size: 28px;
    top: 20px !important;
    right: 20px !important;
    margin-right: 0;
    z-index: 99999999 !important;
    background: rgba(239, 68, 68, 0.9);
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.3);
  }

  .mobile-nav-active .mobile-nav-toggle span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
    background: #fff;
  }

  .mobile-nav-active .mobile-nav-toggle span:nth-child(2) {
    opacity: 0;
    background: #fff;
  }

  .mobile-nav-active .mobile-nav-toggle span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
    background: #fff;
  }

  .mobile-nav-active .mobile-nav-toggle:hover {
    background: rgba(239, 68, 68, 1);
    transform: rotate(90deg) scale(1.1);
  }

  .mobile-nav-active .navmenu {
    position: fixed !important;
    overflow: hidden;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: linear-gradient(
      135deg,
      rgba(15, 23, 42, 0.95),
      rgba(30, 41, 59, 0.9)
    );
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999999 !important;
    animation: fadeInOverlay 0.4s ease-out;
  }

  @keyframes fadeInOverlay {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(10px);
    }
  }

  .mobile-nav-active .navmenu > ul {
    display: block;
    transform: translateY(0);
    opacity: 1;
    animation: slideInMenu 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s both;
  }

  @keyframes slideInMenu {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* 移动端触摸优化 */
  @media (max-width: 768px) {
    .mobile-nav-toggle {
      width: 52px;
      height: 52px;
      font-size: 28px;
      -webkit-tap-highlight-color: transparent;
      touch-action: manipulation;
    }

    .navmenu a {
      padding: 20px 30px;
      font-size: 18px;
      min-height: 60px;
      -webkit-tap-highlight-color: transparent;
      touch-action: manipulation;
    }

    .navmenu ul {
      inset: 90px 15px 40px 15px;
      padding: 25px 0;
    }

    /* 添加菜单项之间的分隔线 */
    .navmenu a:not(:last-child)::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 30px;
      right: 30px;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(59, 130, 246, 0.1),
        transparent
      );
    }

    /* 为菜单项添加涟漪效果 */
    .navmenu a {
      position: relative;
      overflow: hidden;
    }

    .navmenu a:active::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(59, 130, 246, 0.2);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: ripple 0.6s ease-out;
    }

    @keyframes ripple {
      to {
        width: 300px;
        height: 300px;
        opacity: 0;
      }
    }
  }
}

/*--------------------------------------------------------------
# Global Footer
--------------------------------------------------------------*/
.footer {
  color: var(--default-color);
  background-color: var(--background-color);
  font-size: 14px;
  position: relative;
}

.footer .copyright {
  padding: 25px 0;
  border-top: 1px solid
    color-mix(in srgb, var(--default-color), transparent 90%);
}

.footer .copyright p {
  margin-bottom: 0;
}

.footer .credits {
  margin-top: 6px;
  font-size: 13px;
}

.footer .credits a {
  color: #eee;
  text-decoration: underline;
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 999999;
  overflow: hidden;
  background: var(--background-color);
  transition: all 0.6s ease-out;
}

#preloader:before {
  content: "";
  position: fixed;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
  border: 6px solid #ffffff;
  border-color: var(--accent-color) transparent var(--accent-color) transparent;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: animate-preloader 1.5s linear infinite;
}

@keyframes animate-preloader {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: 15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 40px;
  height: 40px;
  border-radius: 4px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 80px 0;
  scroll-margin-top: 100px;
  overflow: clip;
  position: relative;
}

@media (max-width: 1199px) {
  section,
  .section {
    scroll-margin-top: 66px;
    padding: 60px 0;
  }
}

@media (max-width: 768px) {
  section,
  .section {
    padding: 40px 0;
  }
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.section-title h2 {
  color: var(--heading-color);
  font-size: 2.5rem;
  font-weight: 700;
  position: relative;
  margin-bottom: 20px;
  line-height: 1.2;
}

.section-title h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  border-radius: 2px;
}

.section-title p {
  margin-bottom: 0;
  font-size: 1.125rem;
  color: var(--muted-color);
  line-height: 1.6;
  margin-top: 20px;
}

@media (max-width: 768px) {
  .section-title {
    padding-bottom: 40px;
  }

  .section-title h2 {
    font-size: 2rem;
  }

  .section-title p {
    font-size: 1rem;
  }
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  width: 100%;
  min-height: 56vh;
  position: relative;
  padding: 194px 0 80px 0;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 100%
  );
  z-index: 1;
}

.hero-bg-video {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 0 !important;
  overflow: hidden !important;
}

.hero-bg-video video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  transform: translate(-50%, -50%);
  object-fit: cover;
  z-index: 0;
}

.hero .container {
  position: relative;
  z-index: 2;
}

.hero h2 {
  margin: 0 0 24px 0;
  font-size: 3rem;
  color: #ffffff;
  font-weight: 700;
  line-height: 1.2;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.hero h2 span {
  display: inline-block;
  position: relative;
}

.hero p {
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: 40px;
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 1.6;
  max-width: 580px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .hero {
    min-height: 80vh;
    padding: 80px 0 40px 0;
  }

  .hero h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }

  .hero p {
    font-size: 1.125rem;
    margin-bottom: 32px;
  }
}

@media (max-width: 480px) {
  .hero h2 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1rem;
  }
}

.hero .btn-get-started {
  color: #1e40af;
  background: #ffffff;
  font-family: var(--heading-font);
  font-weight: 600;
  font-size: 16px;
  display: inline-block;
  padding: 16px 32px;
  border-radius: 30px;
  transition: all 0.3s ease;
  border: none;
  text-decoration: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.hero .btn-get-started:hover {
  background: #f8fafc;
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  color: #1e40af;
}

/*--------------------------------------------------------------
# Advantage Section
--------------------------------------------------------------*/
.advantage {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
}

.advantage::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59,130,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

/* 强制应用Bootstrap间距 */
.advantage .row.g-4 {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 1.5rem;
  margin-left: calc(-0.5 * var(--bs-gutter-x));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
}

.advantage .row.g-4 > * {
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  margin-bottom: var(--bs-gutter-y);
}

/* 额外的间距确保 */
@media (min-width: 992px) {
  .advantage .col-lg-6 {
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-bottom: 30px !important;
  }
}

/* 强制间距 - 使用margin方式 */
.advantage .icon-box {
  margin-left: 15px !important;
  margin-right: 15px !important;
  margin-bottom: 30px !important;
}

.advantage .content h3 {
  font-weight: 700;
  font-size: 2.25rem;
  margin-bottom: 24px;
  color: var(--heading-color);
  line-height: 1.3;
}

.advantage .content p {
  margin-bottom: 32px;
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--muted-color);
}

.advantage .icon-box {
  background: #ffffff;
  padding: 28px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.advantage .icon-box::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.advantage .icon-box:hover::before {
  transform: scaleX(1);
}

.advantage .icon-box:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color);
}

.advantage .icon-box i {
  font-size: 2rem;
  color: var(--accent-color);
  margin-bottom: 12px;
  margin-right: 12px;
  display: inline-block;
}

.advantage .icon-box h4 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.advantage .icon-box h4 a {
  color: var(--heading-color);
  transition: all 0.3s ease;
  text-decoration: none;
}

.advantage .icon-box p {
  font-size: 0.95rem;
  color: var(--muted-color);
  margin-bottom: 0;
  line-height: 1.6;
}

.advantage .icon-box:hover h4 a {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .advantage .content h3 {
    font-size: 1.875rem;
    text-align: center;
    margin-bottom: 20px;
  }

  .advantage .content p {
    margin-bottom: 24px;
    font-size: 1rem;
    text-align: center;
  }

  .advantage .icon-box {
    padding: 24px 20px;
    margin-bottom: 20px;
  }
}

/*--------------------------------------------------------------
# Clients Section
--------------------------------------------------------------*/
.clients {
  padding: 30px 0 30px 0;
}
.clients .swiper {
  padding: 10px 0;
}

.clients .swiper-wrapper {
  height: auto;
}

.clients .swiper-slide img {
  transition: 0.3s;
}

.clients .swiper-slide img:hover {
  transform: scale(1.1);
}
@media (max-width: 991.98px) {
  .clients {
    padding: 0px !important;
  }
  .clients .container {
    padding: 0px !important;
  }
}
/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  position: relative;
}

.services::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(59,130,246,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.5;
}

.services .container {
  position: relative;
  z-index: 1;
}

/* 强制应用Bootstrap间距 - Services部分 */
.services .row.g-4 {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 1.5rem;
  margin-left: calc(-0.5 * var(--bs-gutter-x));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
}

.services .row.g-4 > * {
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  margin-bottom: var(--bs-gutter-y);
}

/* 额外的间距确保 - Services部分 */
@media (min-width: 992px) {
  .services .col-lg-4 {
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-bottom: 30px !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .services .col-md-6 {
    padding-left: 15px !important;
    padding-right: 15px !important;
    margin-bottom: 30px !important;
  }
}

/* 强制间距 - 使用margin方式 */
.services .service-item {
  margin-left: 15px !important;
  margin-right: 15px !important;
  margin-bottom: 0px !important;
}

/* 使用padding方式确保间距 */
.services .col-lg-4,
.services .col-md-6 {
  padding: 15px !important;
}

.advantage .col-lg-6 {
  padding: 15px !important;
}

.services .service-item {
  position: relative;
  height: 100%;
  background: #ffffff;
  padding: 32px 24px;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  overflow: hidden;
  margin-bottom: 24px;
}

.services .service-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.services .service-item:hover::before {
  transform: scaleX(1);
}

.services .service-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 32px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color);
}

.services .service-item .icon {
  margin-right: 0;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 15px;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1),
    rgba(37, 99, 235, 0.1)
  );
  transition: all 0.3s ease;
}

.services .service-item .icon i {
  color: var(--accent-color);
  font-size: 28px;
  line-height: 0;
  transition: all 0.3s ease;
}

.services .service-item:hover .icon {
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  transform: scale(1.1);
}

.services .service-item:hover .icon i {
  color: #ffffff;
  transform: scale(1.1);
}

.services .service-item .title {
  color: var(--heading-color);
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 1.25rem;
  line-height: 1.4;
  transition: all 0.3s ease;
}

.services .service-item .description {
  font-size: 0.95rem;
  color: var(--muted-color);
  margin-bottom: 20px;
  line-height: 1.6;
}

.services .service-item .readmore {
  display: inline-flex;
  align-items: center;
  color: var(--accent-color);
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.875rem;
  text-decoration: none;
  padding: 8px 0;
  border-bottom: 1px solid transparent;
}

.services .service-item .readmore i {
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.services .service-item:hover .title {
  color: var(--accent-color);
}

.services .service-item:hover .readmore {
  border-bottom-color: var(--accent-color);
}

.services .service-item:hover .readmore i {
  transform: translateX(4px);
}

@media (max-width: 768px) {
  .services .service-item {
    padding: 32px 24px;
    margin-bottom: 24px;
  }

  .services .service-item .icon {
    width: 64px;
    height: 64px;
    margin-bottom: 20px;
  }

  .services .service-item .icon i {
    font-size: 28px;
  }

  .services .service-item .title {
    font-size: 1.125rem;
    margin-bottom: 12px;
  }
}

/*--------------------------------------------------------------
# Call To Action Section
--------------------------------------------------------------*/
.call-to-action {
  --background-color: none;
}

.call-to-action .container {
  padding-top: 80px;
  padding-bottom: 80px;
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  clip-path: inset(0 round 15px);
}

.call-to-action .container img {
  position: fixed;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  border-radius: 15px;
  overflow: hidden;
}

.call-to-action .container:before {
  content: "";
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  inset: 0;
  z-index: 2;
}

.call-to-action .container .content {
  position: relative;
  z-index: 3;
}

.call-to-action p {
  color: var(--default-color);
  margin-bottom: 20px;
  margin-top: 20px;
}

.call-to-action .pulsating-play-btn {
  display: inline-block;
}

.call-to-action .cta-btn {
  font-family: var(--heading-font);
  font-weight: 500;
  font-size: 16px;
  letter-spacing: 1px;
  display: inline-block;
  padding: 12px 40px;
  border-radius: 5px;
  transition: 0.5s;
  margin: 10px;
  border: 2px solid var(--contrast-color);
  color: var(--contrast-color);
}

.call-to-action .cta-btn:hover {
  background: var(--accent-color);
  border: 2px solid var(--accent-color);
}

/*--------------------------------------------------------------
# Faq Section
--------------------------------------------------------------*/
.faq .faq-container .faq-item {
  position: relative;
  padding: 20px 0;
  border-bottom: 1px solid
    color-mix(in srgb, var(--default-color), transparent 85%);
  overflow: hidden;
}

.faq .faq-container .faq-item:last-child {
  margin-bottom: 0;
}

.faq .faq-container .faq-item h3 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  margin: 0 30px 0 0;
  transition: 0.3s;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.faq .faq-container .faq-item h3 .num {
  color: var(--accent-color);
  padding-right: 5px;
}

.faq .faq-container .faq-item h3:hover {
  color: var(--accent-color);
}

.faq .faq-container .faq-item .faq-content {
  display: grid;
  grid-template-rows: 0fr;
  transition: 0.3s ease-in-out;
  visibility: hidden;
  opacity: 0;
}

.faq .faq-container .faq-item .faq-content p {
  margin-bottom: 0;
  overflow: hidden;
}

.faq .faq-container .faq-item .faq-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 16px;
  line-height: 0;
  transition: 0.3s;
  cursor: pointer;
}

.faq .faq-container .faq-item .faq-toggle:hover {
  color: var(--accent-color);
}

.faq .faq-container .faq-active h3 {
  color: var(--accent-color);
}

.faq .faq-container .faq-active .faq-content {
  grid-template-rows: 1fr;
  visibility: visible;
  opacity: 1;
  padding-top: 10px;
}

.faq .faq-container .faq-active .faq-toggle {
  transform: rotate(90deg);
  color: var(--accent-color);
}

/*--------------------------------------------------------------
# Scene Section
--------------------------------------------------------------*/
.scene {
  background: #ffffff;
  position: relative;
}

.scene .container {
  position: relative;
  z-index: 1;
}

.category {
  display: block;
  padding: 32px 24px;
  background: #ffffff;
  border-radius: 16px;
  text-decoration: none !important;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.category::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.category:hover::before {
  transform: scaleX(1);
}

.category:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 32px rgba(59, 130, 246, 0.15);
  border-color: var(--accent-color);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.category .bi {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  color: #ffffff;
  font-size: 24px;
  transition: all 0.3s ease;
}

.category:hover .bi {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.category h3 {
  font-family: var(--heading-font);
  margin: 0 0 12px 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.3;
  color: var(--heading-color);
  transition: all 0.3s ease;
}

.category span {
  font-size: 0.95rem;
  color: var(--muted-color);
  line-height: 1.6;
  transition: all 0.3s ease;
}

.category:hover h3 {
  color: var(--accent-color);
}

.category:hover span {
  color: var(--default-color);
}

@media (max-width: 768px) {
  .category {
    padding: 28px 24px;
    margin-bottom: 20px;
    min-height: 180px;
  }

  .category .bi {
    width: 52px;
    height: 52px;
    font-size: 22px;
    margin-bottom: 20px;
  }

  .category h3 {
    font-size: 1.1rem;
    margin-bottom: 10px;
  }

  .category span {
    font-size: 0.9rem;
  }
}
