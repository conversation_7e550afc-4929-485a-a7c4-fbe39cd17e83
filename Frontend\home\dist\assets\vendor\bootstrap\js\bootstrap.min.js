/*!
  * Bootstrap v5.3.5 (https://getbootstrap.com/)
  * Copyright 2011-2025 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrap=e(t.Popper)}(this,(function(t){"use strict";const e=function(t){const e=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(t)for(const i in t)if("default"!==i){const s=Object.getOwnPropertyDescriptor(t,i);Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>t[i]})}return e.default=t,Object.freeze(e)}(t),i=new Map,s={set(t,e,s){i.has(t)||i.set(t,new Map);const n=i.get(t);n.has(e)||0===n.size?n.set(e,s):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`)},get:(t,e)=>i.has(t)&&i.get(t).get(e)||null,remove(t,e){if(!i.has(t))return;const s=i.get(t);s.delete(e),0===s.size&&i.delete(t)}},n="transitionend",o=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),r=t=>{t.dispatchEvent(new Event(n))},a=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),l=t=>a(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(o(t)):null,c=t=>{if(!a(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),i=t.closest("details:not([open])");if(!i)return e;if(i!==t){const e=t.closest("summary");if(e&&e.parentNode!==i)return!1;if(null===e)return!1}return e},h=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled")),d=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?d(t.parentNode):null},u=()=>{},_=t=>{t.offsetHeight},g=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,f=[],m=()=>"rtl"===document.documentElement.dir,p=t=>{var e;e=()=>{const e=g();if(e){const i=t.NAME,s=e.fn[i];e.fn[i]=t.jQueryInterface,e.fn[i].Constructor=t,e.fn[i].noConflict=()=>(e.fn[i]=s,t.jQueryInterface)}},"loading"===document.readyState?(f.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of f)t()})),f.push(e)):e()},b=(t,e=[],i=t)=>"function"==typeof t?t.call(...e):i,v=(t,e,i=!0)=>{if(!i)return void b(t);const s=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);const s=Number.parseFloat(e),n=Number.parseFloat(i);return s||n?(e=e.split(",")[0],i=i.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(i))):0})(e)+5;let o=!1;const a=({target:i})=>{i===e&&(o=!0,e.removeEventListener(n,a),b(t))};e.addEventListener(n,a),setTimeout((()=>{o||r(e)}),s)},y=(t,e,i,s)=>{const n=t.length;let o=t.indexOf(e);return-1===o?!i&&s?t[n-1]:t[0]:(o+=i?1:-1,s&&(o=(o+n)%n),t[Math.max(0,Math.min(o,n-1))])},w=/[^.]*(?=\..*)\.|.*/,A=/\..*/,E=/::\d+$/,C={};let T=1;const k={mouseenter:"mouseover",mouseleave:"mouseout"},$=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function S(t,e){return e&&`${e}::${T++}`||t.uidEvent||T++}function L(t){const e=S(t);return t.uidEvent=e,C[e]=C[e]||{},C[e]}function O(t,e,i=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===i))}function I(t,e,i){const s="string"==typeof e,n=s?i:e||i;let o=x(t);return $.has(o)||(o=t),[s,n,o]}function D(t,e,i,s,n){if("string"!=typeof e||!t)return;let[o,r,a]=I(e,i,s);if(e in k){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};r=t(r)}const l=L(t),c=l[a]||(l[a]={}),h=O(c,r,o?i:null);if(h)return void(h.oneOff=h.oneOff&&n);const d=S(r,e.replace(w,"")),u=o?function(t,e,i){return function s(n){const o=t.querySelectorAll(e);for(let{target:r}=n;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return j(n,{delegateTarget:r}),s.oneOff&&M.off(t,n.type,e,i),i.apply(r,[n])}}(t,i,r):function(t,e){return function i(s){return j(s,{delegateTarget:t}),i.oneOff&&M.off(t,s.type,e),e.apply(t,[s])}}(t,r);u.delegationSelector=o?i:null,u.callable=r,u.oneOff=n,u.uidEvent=d,c[d]=u,t.addEventListener(a,u,o)}function N(t,e,i,s,n){const o=O(e[i],s,n);o&&(t.removeEventListener(i,o,Boolean(n)),delete e[i][o.uidEvent])}function P(t,e,i,s){const n=e[i]||{};for(const[o,r]of Object.entries(n))o.includes(s)&&N(t,e,i,r.callable,r.delegationSelector)}function x(t){return t=t.replace(A,""),k[t]||t}const M={on(t,e,i,s){D(t,e,i,s,!1)},one(t,e,i,s){D(t,e,i,s,!0)},off(t,e,i,s){if("string"!=typeof e||!t)return;const[n,o,r]=I(e,i,s),a=r!==e,l=L(t),c=l[r]||{},h=e.startsWith(".");if(void 0===o){if(h)for(const i of Object.keys(l))P(t,l,i,e.slice(1));for(const[i,s]of Object.entries(c)){const n=i.replace(E,"");a&&!e.includes(n)||N(t,l,r,s.callable,s.delegationSelector)}}else{if(!Object.keys(c).length)return;N(t,l,r,o,n?i:null)}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const s=g();let n=null,o=!0,r=!0,a=!1;e!==x(e)&&s&&(n=s.Event(e,i),s(t).trigger(n),o=!n.isPropagationStopped(),r=!n.isImmediatePropagationStopped(),a=n.isDefaultPrevented());const l=j(new Event(e,{bubbles:o,cancelable:!0}),i);return a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&n&&n.preventDefault(),l}};function j(t,e={}){for(const[i,s]of Object.entries(e))try{t[i]=s}catch(e){Object.defineProperty(t,i,{configurable:!0,get:()=>s})}return t}function F(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function z(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const H={setDataAttribute(t,e,i){t.setAttribute(`data-bs-${z(e)}`,i)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${z(e)}`)},getDataAttributes(t){if(!t)return{};const e={},i=Object.keys(t.dataset).filter((t=>t.startsWith("bs")&&!t.startsWith("bsConfig")));for(const s of i){let i=s.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1),e[i]=F(t.dataset[s])}return e},getDataAttribute:(t,e)=>F(t.getAttribute(`data-bs-${z(e)}`))};class B{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=a(e)?H.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...a(e)?H.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[s,n]of Object.entries(e)){const e=t[s],o=a(e)?"element":null==(i=e)?`${i}`:Object.prototype.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(n).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${s}" provided type "${o}" but expected type "${n}".`)}var i}}class q extends B{constructor(t,e){super(),(t=l(t))&&(this._element=t,this._config=this._getConfig(e),s.set(this._element,this.constructor.DATA_KEY,this))}dispose(){s.remove(this._element,this.constructor.DATA_KEY),M.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){v(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return s.get(l(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.5"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const W=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let i=t.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),e=i&&"#"!==i?i.trim():null}return e?e.split(",").map((t=>o(t))).join(","):null},R={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const i=[];let s=t.parentNode.closest(e);for(;s;)i.push(s),s=s.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!h(t)&&c(t)))},getSelectorFromElement(t){const e=W(t);return e&&R.findOne(e)?e:null},getElementFromSelector(t){const e=W(t);return e?R.findOne(e):null},getMultipleElementsFromSelector(t){const e=W(t);return e?R.find(e):[]}},K=(t,e="hide")=>{const i=`click.dismiss${t.EVENT_KEY}`,s=t.NAME;M.on(document,i,`[data-bs-dismiss="${s}"]`,(function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),h(this))return;const n=R.getElementFromSelector(this)||this.closest(`.${s}`);t.getOrCreateInstance(n)[e]()}))},V=".bs.alert",Q=`close${V}`,X=`closed${V}`;class Y extends q{static get NAME(){return"alert"}close(){if(M.trigger(this._element,Q).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),M.trigger(this._element,X),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=Y.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}K(Y,"close"),p(Y);const U='[data-bs-toggle="button"]';class G extends q{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each((function(){const e=G.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}M.on(document,"click.bs.button.data-api",U,(t=>{t.preventDefault();const e=t.target.closest(U);G.getOrCreateInstance(e).toggle()})),p(G);const J=".bs.swipe",Z=`touchstart${J}`,tt=`touchmove${J}`,et=`touchend${J}`,it=`pointerdown${J}`,st=`pointerup${J}`,nt={endCallback:null,leftCallback:null,rightCallback:null},ot={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class rt extends B{constructor(t,e){super(),this._element=t,t&&rt.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return nt}static get DefaultType(){return ot}static get NAME(){return"swipe"}dispose(){M.off(this._element,J)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),b(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&b(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(M.on(this._element,it,(t=>this._start(t))),M.on(this._element,st,(t=>this._end(t))),this._element.classList.add("pointer-event")):(M.on(this._element,Z,(t=>this._start(t))),M.on(this._element,tt,(t=>this._move(t))),M.on(this._element,et,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const at=".bs.carousel",lt=".data-api",ct="ArrowLeft",ht="ArrowRight",dt="next",ut="prev",_t="left",gt="right",ft=`slide${at}`,mt=`slid${at}`,pt=`keydown${at}`,bt=`mouseenter${at}`,vt=`mouseleave${at}`,yt=`dragstart${at}`,wt=`load${at}${lt}`,At=`click${at}${lt}`,Et="carousel",Ct="active",Tt=".active",kt=".carousel-item",$t=Tt+kt,St={[ct]:gt,[ht]:_t},Lt={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Ot={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class It extends q{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=R.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===Et&&this.cycle()}static get Default(){return Lt}static get DefaultType(){return Ot}static get NAME(){return"carousel"}next(){this._slide(dt)}nextWhenVisible(){!document.hidden&&c(this._element)&&this.next()}prev(){this._slide(ut)}pause(){this._isSliding&&r(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?M.one(this._element,mt,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void M.one(this._element,mt,(()=>this.to(t)));const i=this._getItemIndex(this._getActive());if(i===t)return;const s=t>i?dt:ut;this._slide(s,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&M.on(this._element,pt,(t=>this._keydown(t))),"hover"===this._config.pause&&(M.on(this._element,bt,(()=>this.pause())),M.on(this._element,vt,(()=>this._maybeEnableCycle()))),this._config.touch&&rt.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of R.find(".carousel-item img",this._element))M.on(t,yt,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(_t)),rightCallback:()=>this._slide(this._directionToOrder(gt)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new rt(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=St[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=R.findOne(Tt,this._indicatorsElement);e.classList.remove(Ct),e.removeAttribute("aria-current");const i=R.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);i&&(i.classList.add(Ct),i.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),s=t===dt,n=e||y(this._getItems(),i,s,this._config.wrap);if(n===i)return;const o=this._getItemIndex(n),r=e=>M.trigger(this._element,e,{relatedTarget:n,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:o});if(r(ft).defaultPrevented)return;if(!i||!n)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=n;const l=s?"carousel-item-start":"carousel-item-end",c=s?"carousel-item-next":"carousel-item-prev";n.classList.add(c),_(n),i.classList.add(l),n.classList.add(l),this._queueCallback((()=>{n.classList.remove(l,c),n.classList.add(Ct),i.classList.remove(Ct,c,l),this._isSliding=!1,r(mt)}),i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return R.findOne($t,this._element)}_getItems(){return R.find(kt,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return m()?t===_t?ut:dt:t===_t?dt:ut}_orderToDirection(t){return m()?t===ut?_t:gt:t===ut?gt:_t}static jQueryInterface(t){return this.each((function(){const e=It.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}M.on(document,At,"[data-bs-slide], [data-bs-slide-to]",(function(t){const e=R.getElementFromSelector(this);if(!e||!e.classList.contains(Et))return;t.preventDefault();const i=It.getOrCreateInstance(e),s=this.getAttribute("data-bs-slide-to");return s?(i.to(s),void i._maybeEnableCycle()):"next"===H.getDataAttribute(this,"slide")?(i.next(),void i._maybeEnableCycle()):(i.prev(),void i._maybeEnableCycle())})),M.on(window,wt,(()=>{const t=R.find('[data-bs-ride="carousel"]');for(const e of t)It.getOrCreateInstance(e)})),p(It);const Dt=".bs.collapse",Nt=`show${Dt}`,Pt=`shown${Dt}`,xt=`hide${Dt}`,Mt=`hidden${Dt}`,jt=`click${Dt}.data-api`,Ft="show",zt="collapse",Ht="collapsing",Bt=`:scope .${zt} .${zt}`,qt='[data-bs-toggle="collapse"]',Wt={parent:null,toggle:!0},Rt={parent:"(null|element)",toggle:"boolean"};class Kt extends q{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=R.find(qt);for(const t of i){const e=R.getSelectorFromElement(t),i=R.find(e).filter((t=>t===this._element));null!==e&&i.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Wt}static get DefaultType(){return Rt}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((t=>t!==this._element)).map((t=>Kt.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(M.trigger(this._element,Nt).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(zt),this._element.classList.add(Ht),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Ht),this._element.classList.add(zt,Ft),this._element.style[e]="",M.trigger(this._element,Pt)}),this._element,!0),this._element.style[e]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(M.trigger(this._element,xt).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,_(this._element),this._element.classList.add(Ht),this._element.classList.remove(zt,Ft);for(const t of this._triggerArray){const e=R.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(Ht),this._element.classList.add(zt),M.trigger(this._element,Mt)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(Ft)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=l(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(qt);for(const e of t){const t=R.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=R.find(Bt,this._config.parent);return R.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle("collapsed",!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const i=Kt.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}}))}}M.on(document,jt,qt,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of R.getMultipleElementsFromSelector(this))Kt.getOrCreateInstance(t,{toggle:!1}).toggle()})),p(Kt);const Vt="dropdown",Qt=".bs.dropdown",Xt=".data-api",Yt="ArrowUp",Ut="ArrowDown",Gt=`hide${Qt}`,Jt=`hidden${Qt}`,Zt=`show${Qt}`,te=`shown${Qt}`,ee=`click${Qt}${Xt}`,ie=`keydown${Qt}${Xt}`,se=`keyup${Qt}${Xt}`,ne="show",oe='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',re=`${oe}.${ne}`,ae=".dropdown-menu",le=m()?"top-end":"top-start",ce=m()?"top-start":"top-end",he=m()?"bottom-end":"bottom-start",de=m()?"bottom-start":"bottom-end",ue=m()?"left-start":"right-start",_e=m()?"right-start":"left-start",ge={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},fe={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class me extends q{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=R.next(this._element,ae)[0]||R.prev(this._element,ae)[0]||R.findOne(ae,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return ge}static get DefaultType(){return fe}static get NAME(){return Vt}toggle(){return this._isShown()?this.hide():this.show()}show(){if(h(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!M.trigger(this._element,Zt,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))M.on(t,"mouseover",u);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(ne),this._element.classList.add(ne),M.trigger(this._element,te,t)}}hide(){if(h(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!M.trigger(this._element,Gt,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))M.off(t,"mouseover",u);this._popper&&this._popper.destroy(),this._menu.classList.remove(ne),this._element.classList.remove(ne),this._element.setAttribute("aria-expanded","false"),H.removeDataAttribute(this._menu,"popper"),M.trigger(this._element,Jt,t)}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!a(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${Vt.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===e)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org/docs/v2/)");let t=this._element;"parent"===this._config.reference?t=this._parent:a(this._config.reference)?t=l(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const i=this._getPopperConfig();this._popper=e.createPopper(t,this._menu,i)}_isShown(){return this._menu.classList.contains(ne)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return ue;if(t.classList.contains("dropstart"))return _e;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?ce:le:e?de:he}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(H.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...b(this._config.popperConfig,[void 0,t])}}_selectMenuItem({key:t,target:e}){const i=R.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((t=>c(t)));i.length&&y(i,e,t===Ut,!i.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=me.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=R.find(re);for(const i of e){const e=me.getInstance(i);if(!e||!1===e._config.autoClose)continue;const s=t.composedPath(),n=s.includes(e._menu);if(s.includes(e._element)||"inside"===e._config.autoClose&&!n||"outside"===e._config.autoClose&&n)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i="Escape"===t.key,s=[Yt,Ut].includes(t.key);if(!s&&!i)return;if(e&&!i)return;t.preventDefault();const n=this.matches(oe)?this:R.prev(this,oe)[0]||R.next(this,oe)[0]||R.findOne(oe,t.delegateTarget.parentNode),o=me.getOrCreateInstance(n);if(s)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),n.focus())}}M.on(document,ie,oe,me.dataApiKeydownHandler),M.on(document,ie,ae,me.dataApiKeydownHandler),M.on(document,ee,me.clearMenus),M.on(document,se,me.clearMenus),M.on(document,ee,oe,(function(t){t.preventDefault(),me.getOrCreateInstance(this).toggle()})),p(me);const pe="backdrop",be="show",ve=`mousedown.bs.${pe}`,ye={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},we={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Ae extends B{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return ye}static get DefaultType(){return we}static get NAME(){return pe}show(t){if(!this._config.isVisible)return void b(t);this._append();const e=this._getElement();this._config.isAnimated&&_(e),e.classList.add(be),this._emulateAnimation((()=>{b(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(be),this._emulateAnimation((()=>{this.dispose(),b(t)}))):b(t)}dispose(){this._isAppended&&(M.off(this._element,ve),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=l(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),M.on(t,ve,(()=>{b(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){v(t,this._getElement(),this._config.isAnimated)}}const Ee=".bs.focustrap",Ce=`focusin${Ee}`,Te=`keydown.tab${Ee}`,ke="backward",$e={autofocus:!0,trapElement:null},Se={autofocus:"boolean",trapElement:"element"};class Le extends B{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return $e}static get DefaultType(){return Se}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),M.off(document,Ee),M.on(document,Ce,(t=>this._handleFocusin(t))),M.on(document,Te,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,M.off(document,Ee))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=R.focusableChildren(e);0===i.length?e.focus():this._lastTabNavDirection===ke?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?ke:"forward")}}const Oe=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Ie=".sticky-top",De="padding-right",Ne="margin-right";class Pe{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,De,(e=>e+t)),this._setElementAttributes(Oe,De,(e=>e+t)),this._setElementAttributes(Ie,Ne,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,De),this._resetElementAttributes(Oe,De),this._resetElementAttributes(Ie,Ne)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const s=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+s)return;this._saveInitialAttribute(t,e);const n=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${i(Number.parseFloat(n))}px`)}))}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&H.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const i=H.getDataAttribute(t,e);null!==i?(H.removeDataAttribute(t,e),t.style.setProperty(e,i)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(a(t))e(t);else for(const i of R.find(t,this._element))e(i)}}const xe=".bs.modal",Me=`hide${xe}`,je=`hidePrevented${xe}`,Fe=`hidden${xe}`,ze=`show${xe}`,He=`shown${xe}`,Be=`resize${xe}`,qe=`click.dismiss${xe}`,We=`mousedown.dismiss${xe}`,Re=`keydown.dismiss${xe}`,Ke=`click${xe}.data-api`,Ve="modal-open",Qe="show",Xe="modal-static",Ye={backdrop:!0,focus:!0,keyboard:!0},Ue={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Ge extends q{constructor(t,e){super(t,e),this._dialog=R.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Pe,this._addEventListeners()}static get Default(){return Ye}static get DefaultType(){return Ue}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||M.trigger(this._element,ze,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Ve),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){this._isShown&&!this._isTransitioning&&(M.trigger(this._element,Me).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Qe),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){M.off(window,xe),M.off(this._dialog,xe),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Ae({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Le({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=R.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),_(this._element),this._element.classList.add(Qe),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,M.trigger(this._element,He,{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){M.on(this._element,Re,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),M.on(window,Be,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),M.on(this._element,We,(t=>{M.one(this._element,qe,(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(Ve),this._resetAdjustments(),this._scrollBar.reset(),M.trigger(this._element,Fe)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(M.trigger(this._element,je).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(Xe)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(Xe),this._queueCallback((()=>{this._element.classList.remove(Xe),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const t=m()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!i&&t){const t=m()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const i=Ge.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t](e)}}))}}M.on(document,Ke,'[data-bs-toggle="modal"]',(function(t){const e=R.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),M.one(e,ze,(t=>{t.defaultPrevented||M.one(e,Fe,(()=>{c(this)&&this.focus()}))}));const i=R.findOne(".modal.show");i&&Ge.getInstance(i).hide(),Ge.getOrCreateInstance(e).toggle(this)})),K(Ge),p(Ge);const Je=".bs.offcanvas",Ze=".data-api",ti=`load${Je}${Ze}`,ei="show",ii="showing",si="hiding",ni=".offcanvas.show",oi=`show${Je}`,ri=`shown${Je}`,ai=`hide${Je}`,li=`hidePrevented${Je}`,ci=`hidden${Je}`,hi=`resize${Je}`,di=`click${Je}${Ze}`,ui=`keydown.dismiss${Je}`,_i={backdrop:!0,keyboard:!0,scroll:!1},gi={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class fi extends q{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return _i}static get DefaultType(){return gi}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||M.trigger(this._element,oi,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new Pe).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(ii),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(ei),this._element.classList.remove(ii),M.trigger(this._element,ri,{relatedTarget:t})}),this._element,!0))}hide(){this._isShown&&(M.trigger(this._element,ai).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(si),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove(ei,si),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new Pe).reset(),M.trigger(this._element,ci)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new Ae({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():M.trigger(this._element,li)}:null})}_initializeFocusTrap(){return new Le({trapElement:this._element})}_addEventListeners(){M.on(this._element,ui,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():M.trigger(this._element,li))}))}static jQueryInterface(t){return this.each((function(){const e=fi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}M.on(document,di,'[data-bs-toggle="offcanvas"]',(function(t){const e=R.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),h(this))return;M.one(e,ci,(()=>{c(this)&&this.focus()}));const i=R.findOne(ni);i&&i!==e&&fi.getInstance(i).hide(),fi.getOrCreateInstance(e).toggle(this)})),M.on(window,ti,(()=>{for(const t of R.find(ni))fi.getOrCreateInstance(t).show()})),M.on(window,hi,(()=>{for(const t of R.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&fi.getOrCreateInstance(t).hide()})),K(fi),p(fi);const mi={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},pi=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),bi=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,vi=(t,e)=>{const i=t.nodeName.toLowerCase();return e.includes(i)?!pi.has(i)||Boolean(bi.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(i)))},yi={allowList:mi,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},wi={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Ai={entry:"(string|element|function|null)",selector:"(string|element)"};class Ei extends B{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return yi}static get DefaultType(){return wi}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,i]of Object.entries(this._config.content))this._setContent(t,i,e);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},Ai)}_setContent(t,e,i){const s=R.findOne(i,t);s&&((e=this._resolvePossibleFunction(e))?a(e)?this._putElementInTemplate(l(e),s):this._config.html?s.innerHTML=this._maybeSanitize(e):s.textContent=e:s.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,i){if(!t.length)return t;if(i&&"function"==typeof i)return i(t);const s=(new window.DOMParser).parseFromString(t,"text/html"),n=[].concat(...s.body.querySelectorAll("*"));for(const t of n){const i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}const s=[].concat(...t.attributes),n=[].concat(e["*"]||[],e[i]||[]);for(const e of s)vi(e,n)||t.removeAttribute(e.nodeName)}return s.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return b(t,[void 0,this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const Ci=new Set(["sanitize","allowList","sanitizeFn"]),Ti="fade",ki="show",$i=".tooltip-inner",Si=".modal",Li="hide.bs.modal",Oi="hover",Ii="focus",Di={AUTO:"auto",TOP:"top",RIGHT:m()?"left":"right",BOTTOM:"bottom",LEFT:m()?"right":"left"},Ni={allowList:mi,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Pi={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class xi extends q{constructor(t,i){if(void 0===e)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org/docs/v2/)");super(t,i),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Ni}static get DefaultType(){return Pi}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),M.off(this._element.closest(Si),Li,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=M.trigger(this._element,this.constructor.eventName("show")),e=(d(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:s}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(s.append(i),M.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(ki),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))M.on(t,"mouseover",u);this._queueCallback((()=>{M.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!M.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(ki),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))M.off(t,"mouseover",u);this._activeTrigger.click=!1,this._activeTrigger[Ii]=!1,this._activeTrigger[Oi]=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),M.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(Ti,ki),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(Ti),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Ei({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[$i]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Ti)}_isShown(){return this.tip&&this.tip.classList.contains(ki)}_createPopper(t){const i=b(this._config.placement,[this,t,this._element]),s=Di[i.toUpperCase()];return e.createPopper(this._element,t,this._getPopperConfig(s))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return b(t,[this._element,this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...b(this._config.popperConfig,[void 0,e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)M.on(this._element,this.constructor.eventName("click"),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if("manual"!==e){const t=e===Oi?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),i=e===Oi?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");M.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?Ii:Oi]=!0,e._enter()})),M.on(this._element,i,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?Ii:Oi]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},M.on(this._element.closest(Si),Li,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=H.getDataAttributes(this._element);for(const t of Object.keys(e))Ci.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:l(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=xi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}p(xi);const Mi=".popover-header",ji=".popover-body",Fi={...xi.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},zi={...xi.DefaultType,content:"(null|string|element|function)"};class Hi extends xi{static get Default(){return Fi}static get DefaultType(){return zi}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[Mi]:this._getTitle(),[ji]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=Hi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}p(Hi);const Bi=".bs.scrollspy",qi=`activate${Bi}`,Wi=`click${Bi}`,Ri=`load${Bi}.data-api`,Ki="active",Vi="[href]",Qi=".nav-link",Xi=`${Qi}, .nav-item > ${Qi}, .list-group-item`,Yi={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Ui={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Gi extends q{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Yi}static get DefaultType(){return Ui}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=l(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(M.off(this._config.target,Wi),M.on(this._config.target,Wi,Vi,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,s=e.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:s,behavior:"smooth"});i.scrollTop=s}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},s=(this._rootElement||document.documentElement).scrollTop,n=s>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=s;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(n&&t){if(i(o),!s)return}else n||t||i(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=R.find(Vi,this._config.target);for(const e of t){if(!e.hash||h(e))continue;const t=R.findOne(decodeURI(e.hash),this._element);c(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(Ki),this._activateParents(t),M.trigger(this._element,qi,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))R.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(Ki);else for(const e of R.parents(t,".nav, .list-group"))for(const t of R.prev(e,Xi))t.classList.add(Ki)}_clearActiveClass(t){t.classList.remove(Ki);const e=R.find(`${Vi}.${Ki}`,t);for(const t of e)t.classList.remove(Ki)}static jQueryInterface(t){return this.each((function(){const e=Gi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}M.on(window,Ri,(()=>{for(const t of R.find('[data-bs-spy="scroll"]'))Gi.getOrCreateInstance(t)})),p(Gi);const Ji=".bs.tab",Zi=`hide${Ji}`,ts=`hidden${Ji}`,es=`show${Ji}`,is=`shown${Ji}`,ss=`click${Ji}`,ns=`keydown${Ji}`,os=`load${Ji}`,rs="ArrowLeft",as="ArrowRight",ls="ArrowUp",cs="ArrowDown",hs="Home",ds="End",us="active",_s="fade",gs="show",fs=".dropdown-toggle",ms=`:not(${fs})`,ps='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',bs=`.nav-link${ms}, .list-group-item${ms}, [role="tab"]${ms}, ${ps}`,vs=`.${us}[data-bs-toggle="tab"], .${us}[data-bs-toggle="pill"], .${us}[data-bs-toggle="list"]`;class ys extends q{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),M.on(this._element,ns,(t=>this._keydown(t))))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?M.trigger(e,Zi,{relatedTarget:t}):null;M.trigger(t,es,{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){t&&(t.classList.add(us),this._activate(R.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),M.trigger(t,is,{relatedTarget:e})):t.classList.add(gs)}),t,t.classList.contains(_s)))}_deactivate(t,e){t&&(t.classList.remove(us),t.blur(),this._deactivate(R.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),M.trigger(t,ts,{relatedTarget:e})):t.classList.remove(gs)}),t,t.classList.contains(_s)))}_keydown(t){if(![rs,as,ls,cs,hs,ds].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!h(t)));let i;if([hs,ds].includes(t.key))i=e[t.key===hs?0:e.length-1];else{const s=[as,cs].includes(t.key);i=y(e,t.target,s,!0)}i&&(i.focus({preventScroll:!0}),ys.getOrCreateInstance(i).show())}_getChildren(){return R.find(bs,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=R.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains("dropdown"))return;const s=(t,s)=>{const n=R.findOne(t,i);n&&n.classList.toggle(s,e)};s(fs,us),s(".dropdown-menu",gs),i.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(us)}_getInnerElement(t){return t.matches(bs)?t:R.findOne(bs,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each((function(){const e=ys.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}M.on(document,ss,ps,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),h(this)||ys.getOrCreateInstance(this).show()})),M.on(window,os,(()=>{for(const t of R.find(vs))ys.getOrCreateInstance(t)})),p(ys);const ws=".bs.toast",As=`mouseover${ws}`,Es=`mouseout${ws}`,Cs=`focusin${ws}`,Ts=`focusout${ws}`,ks=`hide${ws}`,$s=`hidden${ws}`,Ss=`show${ws}`,Ls=`shown${ws}`,Os="hide",Is="show",Ds="showing",Ns={animation:"boolean",autohide:"boolean",delay:"number"},Ps={animation:!0,autohide:!0,delay:5e3};class xs extends q{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Ps}static get DefaultType(){return Ns}static get NAME(){return"toast"}show(){M.trigger(this._element,Ss).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(Os),_(this._element),this._element.classList.add(Is,Ds),this._queueCallback((()=>{this._element.classList.remove(Ds),M.trigger(this._element,Ls),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(M.trigger(this._element,ks).defaultPrevented||(this._element.classList.add(Ds),this._queueCallback((()=>{this._element.classList.add(Os),this._element.classList.remove(Ds,Is),M.trigger(this._element,$s)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Is),super.dispose()}isShown(){return this._element.classList.contains(Is)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){M.on(this._element,As,(t=>this._onInteraction(t,!0))),M.on(this._element,Es,(t=>this._onInteraction(t,!1))),M.on(this._element,Cs,(t=>this._onInteraction(t,!0))),M.on(this._element,Ts,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=xs.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}return K(xs),p(xs),{Alert:Y,Button:G,Carousel:It,Collapse:Kt,Dropdown:me,Modal:Ge,Offcanvas:fi,Popover:Hi,ScrollSpy:Gi,Tab:ys,Toast:xs,Tooltip:xi}}));