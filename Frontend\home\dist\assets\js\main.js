!function(){"use strict";function e(){const e=document.querySelector("body"),t=document.querySelector("#header");(t.classList.contains("scroll-up-sticky")||t.classList.contains("sticky-top")||t.classList.contains("fixed-top"))&&(window.scrollY>100?e.classList.add("scrolled"):e.classList.remove("scrolled"))}document.addEventListener("scroll",e),window.addEventListener("load",e);const t=document.querySelector(".mobile-nav-toggle");function o(){document.querySelector("body").classList.toggle("mobile-nav-active"),t.classList.toggle("bi-list"),t.classList.toggle("bi-x")}t.addEventListener("click",o),document.querySelectorAll("#navmenu a").forEach((e=>{e.addEventListener("click",(()=>{document.querySelector(".mobile-nav-active")&&o()}))})),document.querySelectorAll(".navmenu .toggle-dropdown").forEach((e=>{e.addEventListener("click",(function(e){e.preventDefault(),this.parentNode.classList.toggle("active"),this.parentNode.nextElementSibling.classList.toggle("dropdown-active"),e.stopImmediatePropagation()}))}));const i=document.querySelector("#preloader");i&&window.addEventListener("load",(()=>{i.remove()}));let n=document.querySelector(".scroll-top");function r(){n&&(window.scrollY>100?n.classList.add("active"):n.classList.remove("active"))}function l(){AOS.init({duration:600,easing:"ease-in-out",once:!0,mirror:!1})}n.addEventListener("click",(e=>{e.preventDefault(),window.scrollTo({top:0,behavior:"smooth"})})),window.addEventListener("load",r),document.addEventListener("scroll",r),window.addEventListener("load",l),window.addEventListener("load",(function(){document.querySelectorAll(".init-swiper").forEach((function(e){let t=JSON.parse(e.querySelector(".swiper-config").innerHTML.trim());e.classList.contains("swiper-tab")?initSwiperWithCustomPagination(e,t):new Swiper(e,t)}))}));GLightbox({selector:".glightbox"});document.querySelectorAll(".isotope-layout").forEach((function(e){let t,o=e.getAttribute("data-layout")??"masonry",i=e.getAttribute("data-default-filter")??"*",n=e.getAttribute("data-sort")??"original-order";imagesLoaded(e.querySelector(".isotope-container"),(function(){t=new Isotope(e.querySelector(".isotope-container"),{itemSelector:".isotope-item",layoutMode:o,filter:i,sortBy:n})})),e.querySelectorAll(".isotope-filters li").forEach((function(o){o.addEventListener("click",(function(){e.querySelector(".isotope-filters .filter-active").classList.remove("filter-active"),this.classList.add("filter-active"),t.arrange({filter:this.getAttribute("data-filter")}),l()}),!1)}))})),document.querySelectorAll(".faq-item h3, .faq-item .faq-toggle").forEach((e=>{e.addEventListener("click",(()=>{e.parentNode.classList.toggle("faq-active")}))}))}();