/**
 * Help Detail Page JavaScript
 */

// 从URL参数获取文档信息
function getDocumentFromURL() {
  const urlParams = new URLSearchParams(window.location.search);
  const category = urlParams.get('category') || 'getting-started';
  const docId = urlParams.get('doc') || 'quick-start';
  return { category, docId };
}

// 计算阅读时间（基于字数）
function calculateReadingTime(content) {
  const wordsPerMinute = 200; // 平均阅读速度
  const textContent = content.replace(/<[^>]*>/g, ''); // 移除HTML标签
  const wordCount = textContent.length;
  const readingTime = Math.ceil(wordCount / wordsPerMinute / 10); // 中文按字符计算
  return Math.max(1, readingTime); // 最少1分钟
}

// 生成目录
function generateTableOfContents(content) {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = content;
  
  const headings = tempDiv.querySelectorAll('h2, h3, h4');
  const tocList = document.getElementById('tocList');
  
  if (headings.length === 0) {
    document.getElementById('tableOfContents').style.display = 'none';
    return;
  }
  
  tocList.innerHTML = '';
  
  headings.forEach((heading, index) => {
    const id = `heading-${index}`;
    heading.id = id;
    
    const li = document.createElement('li');
    const a = document.createElement('a');
    a.href = `#${id}`;
    a.textContent = heading.textContent;
    a.style.paddingLeft = `${(parseInt(heading.tagName.charAt(1)) - 2) * 16}px`;
    
    li.appendChild(a);
    tocList.appendChild(li);
  });
  
  // 更新文档内容中的标题ID
  const articleContent = document.getElementById('articleContent');
  const contentHeadings = articleContent.querySelectorAll('h2, h3, h4');
  contentHeadings.forEach((heading, index) => {
    heading.id = `heading-${index}`;
  });
}

// 加载文档详情
function loadDocumentDetail() {
  const { category, docId } = getDocumentFromURL();
  
  // 从help.js中获取文档数据
  if (!window.documentsData || !documentsData[category]) {
    console.error('Document data not found');
    return;
  }
  
  const categoryData = documentsData[category];
  const document = categoryData.documents.find(doc => doc.id === docId);
  
  if (!document) {
    console.error('Document not found');
    return;
  }
  
  // 更新页面标题
  document.title = `${document.title} - 法宝帮助中心`;
  document.getElementById('pageTitle').textContent = `${document.title} - 法宝帮助中心`;
  
  // 更新面包屑
  document.getElementById('breadcrumbCategory').textContent = categoryData.title;
  document.getElementById('breadcrumbTitle').textContent = document.title;
  
  // 更新文章信息
  document.getElementById('articleCategory').textContent = categoryData.title;
  document.getElementById('articleTitle').textContent = document.title;
  document.getElementById('articleDescription').textContent = document.description;
  document.getElementById('articleUpdateTime').textContent = document.updateTime;
  
  // 计算并显示阅读时间
  const readingTime = calculateReadingTime(document.content);
  document.getElementById('readingTime').textContent = readingTime;
  
  // 加载文档内容
  document.getElementById('articleContent').innerHTML = document.content;
  
  // 生成目录
  generateTableOfContents(document.content);
  
  // 加载同分类文档导航
  loadDocumentNavigation(category, docId);
  
  // 加载相关文档
  loadRelatedDocuments(category, docId);
  
  // 设置上下篇导航
  setupDocumentNavigation(category, docId);
}

// 加载同分类文档导航
function loadDocumentNavigation(currentCategory, currentDocId) {
  const categoryData = documentsData[currentCategory];
  const navList = document.getElementById('documentNavList');
  
  navList.innerHTML = categoryData.documents.map(doc => `
    <li>
      <a href="help-detail.html?category=${currentCategory}&doc=${doc.id}" 
         class="${doc.id === currentDocId ? 'active' : ''}">
        ${doc.title}
      </a>
    </li>
  `).join('');
}

// 加载相关文档
function loadRelatedDocuments(currentCategory, currentDocId) {
  const relatedContainer = document.getElementById('relatedDocuments');
  const allDocuments = [];
  
  // 收集所有文档
  Object.keys(documentsData).forEach(categoryKey => {
    const category = documentsData[categoryKey];
    category.documents.forEach(doc => {
      if (!(categoryKey === currentCategory && doc.id === currentDocId)) {
        allDocuments.push({
          ...doc,
          category: categoryKey,
          categoryTitle: category.title
        });
      }
    });
  });
  
  // 随机选择3个相关文档
  const shuffled = allDocuments.sort(() => 0.5 - Math.random());
  const related = shuffled.slice(0, 3);
  
  relatedContainer.innerHTML = related.map(doc => `
    <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
      <div class="document-card" onclick="window.location.href='help-detail.html?category=${doc.category}&doc=${doc.id}'">
        <div class="doc-icon">
          <i class="${doc.icon}"></i>
        </div>
        <h4>${doc.title}</h4>
        <p>${doc.description}</p>
        <div class="doc-meta">
          <span class="category-tag">${doc.categoryTitle}</span>
          <span class="update-time">更新于 ${doc.updateTime}</span>
        </div>
      </div>
    </div>
  `).join('');
}

// 设置上下篇导航
function setupDocumentNavigation(currentCategory, currentDocId) {
  const categoryData = documentsData[currentCategory];
  const currentIndex = categoryData.documents.findIndex(doc => doc.id === currentDocId);
  
  const prevDocument = currentIndex > 0 ? categoryData.documents[currentIndex - 1] : null;
  const nextDocument = currentIndex < categoryData.documents.length - 1 ? categoryData.documents[currentIndex + 1] : null;
  
  // 设置上一篇
  const prevElement = document.getElementById('prevDocument');
  if (prevDocument) {
    prevElement.style.display = 'block';
    const prevLink = prevElement.querySelector('.nav-link');
    const prevTitle = prevElement.querySelector('.nav-title');
    prevLink.href = `help-detail.html?category=${currentCategory}&doc=${prevDocument.id}`;
    prevTitle.textContent = prevDocument.title;
  }
  
  // 设置下一篇
  const nextElement = document.getElementById('nextDocument');
  if (nextDocument) {
    nextElement.style.display = 'block';
    const nextLink = nextElement.querySelector('.nav-link');
    const nextTitle = nextElement.querySelector('.nav-title');
    nextLink.href = `help-detail.html?category=${currentCategory}&doc=${nextDocument.id}`;
    nextTitle.textContent = nextDocument.title;
  }
}

// 收藏功能
function toggleBookmark() {
  const bookmarkIcon = document.getElementById('bookmarkIcon');
  const bookmarkText = document.getElementById('bookmarkText');
  const actionBtn = bookmarkIcon.closest('.action-btn');
  
  if (actionBtn.classList.contains('bookmarked')) {
    actionBtn.classList.remove('bookmarked');
    bookmarkIcon.className = 'bi bi-bookmark';
    bookmarkText.textContent = '收藏';
  } else {
    actionBtn.classList.add('bookmarked');
    bookmarkIcon.className = 'bi bi-bookmark-fill';
    bookmarkText.textContent = '已收藏';
  }
}

// 分享功能
function shareDocument() {
  if (navigator.share) {
    navigator.share({
      title: document.getElementById('articleTitle').textContent,
      text: document.getElementById('articleDescription').textContent,
      url: window.location.href
    });
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href).then(() => {
      alert('链接已复制到剪贴板');
    });
  }
}

// 打印功能
function printDocument() {
  window.print();
}

// 反馈功能
function submitFeedback(type) {
  const buttons = document.querySelectorAll('.feedback-btn');
  buttons.forEach(btn => btn.style.opacity = '0.5');
  
  // 这里可以发送反馈到服务器
  console.log('Feedback submitted:', type);
  
  setTimeout(() => {
    alert(type === 'helpful' ? '感谢您的反馈！' : '我们会继续改进文档质量。');
    buttons.forEach(btn => btn.style.opacity = '1');
  }, 500);
}

// 平滑滚动到锚点
function setupSmoothScrolling() {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
}

// 高亮当前目录项
function highlightCurrentTOCItem() {
  const headings = document.querySelectorAll('.article-content h2, .article-content h3, .article-content h4');
  const tocLinks = document.querySelectorAll('#tocList a');
  
  function updateActiveTOC() {
    let current = '';
    headings.forEach(heading => {
      const rect = heading.getBoundingClientRect();
      if (rect.top <= 100) {
        current = heading.id;
      }
    });
    
    tocLinks.forEach(link => {
      link.classList.remove('active');
      if (link.getAttribute('href') === `#${current}`) {
        link.classList.add('active');
      }
    });
  }
  
  window.addEventListener('scroll', updateActiveTOC);
  updateActiveTOC(); // 初始调用
}

// 初始化
document.addEventListener('DOMContentLoaded', function() {
  // 初始化AOS动画
  AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    mirror: false
  });
  
  // 加载文档详情
  loadDocumentDetail();
  
  // 设置平滑滚动
  setupSmoothScrolling();
  
  // 设置目录高亮
  setTimeout(highlightCurrentTOC, 1000); // 延迟执行，确保内容已加载
});

// 添加目录高亮样式
const style = document.createElement('style');
style.textContent = `
  #tocList a.active {
    color: var(--accent-color);
    font-weight: 600;
    padding-left: 8px;
  }
`;
document.head.appendChild(style);
