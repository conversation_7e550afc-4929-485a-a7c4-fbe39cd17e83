/*--------------------------------------------------------------
# Help Center Styles
--------------------------------------------------------------*/

/* Help Hero Section */
.help-hero {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 140px 0 80px 0;
  position: relative;
}

.help-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59,130,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.help-hero .container {
  position: relative;
  z-index: 1;
}

.help-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 20px;
  line-height: 1.2;
}

.help-hero p {
  font-size: 1.2rem;
  color: var(--default-color);
  margin-bottom: 40px;
  opacity: 0.8;
}

/* Search Box */
.search-box {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-box .form-control {
  height: 60px;
  padding: 0 60px 0 24px;
  font-size: 16px;
  border: 2px solid rgba(0, 119, 255, 0.1);
  border-radius: 30px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-box .form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 4px 20px rgba(0, 119, 255, 0.2);
  outline: none;
}

.search-box .search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  border: none;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.search-box .search-btn:hover {
  background: var(--accent-hover);
  transform: translateY(-50%) scale(1.05);
}

/* Help Categories Section */
.help-categories {
  padding: 60px 0;
  background: #fafbfc;
}

/* Sidebar */
.help-sidebar {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  position: sticky;
  top: 100px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.help-sidebar h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 4px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background: transparent;
  position: relative;
  min-height: 60px;
}

.category-item:hover {
  background: rgba(0, 119, 255, 0.04);
  border-color: rgba(0, 119, 255, 0.1);
}

.category-item.active {
  background: rgba(0, 119, 255, 0.06);
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.category-icon {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.category-icon img {
  width: 20px;
  height: 20px;
  object-fit: contain;
  opacity: 0.8;
}

.category-item.active .category-icon {
  background: rgba(0, 119, 255, 0.08);
  border-color: rgba(0, 119, 255, 0.2);
}

.category-item.active .category-icon img {
  opacity: 1;
}

.category-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.category-title {
  font-weight: 500;
  font-size: 14px;
  color: var(--heading-color);
  line-height: 1.4;
  max-width: 10ch;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.category-item.active .category-title {
  color: var(--accent-color);
  font-weight: 600;
}

.category-count {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
}

.count-badge {
  background: var(--muted-color);
  color: white;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

.category-item.active .count-badge {
  background: var(--accent-color);
  color: white;
}

/* Document List */
.document-list {
  background: #ffffff;
  border-radius: 12px;
  padding: 32px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.category-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-color);
}

.category-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 8px;
}

.category-header p {
  font-size: 0.95rem;
  color: var(--muted-color);
  margin: 0;
}

.documents-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.document-card {
  background: #ffffff;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px 24px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  min-height: 80px;
  margin-bottom: 8px;
}

.document-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--accent-color);
  transform: scaleY(0);
  transition: transform 0.2s ease;
  border-radius: 0 2px 2px 0;
}

.document-card:hover::before {
  transform: scaleY(1);
}

.document-card:hover {
  border-color: rgba(0, 119, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 119, 255, 0.08);
}

.document-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.document-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 6px;
  line-height: 1.4;
}

.document-card p {
  color: var(--muted-color);
  margin-bottom: 10px;
  font-size: 13px;
  line-height: 1.5;
}

.document-card .doc-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 11px;
  color: var(--muted-color);
}

.document-card .doc-meta .update-time {
  font-style: normal;
}

.document-card .doc-meta .read-more {
  color: var(--accent-color);
  font-weight: 500;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 12px;
}

.document-card .doc-meta .read-more:hover {
  color: var(--accent-hover);
}

.document-card .doc-meta .category-tag {
  background: var(--accent-color);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.document-actions {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.doc-arrow {
  color: var(--muted-color);
  font-size: 14px;
  opacity: 0.5;
  transition: all 0.2s ease;
}

.document-card:hover .doc-arrow {
  opacity: 0.8;
  color: var(--accent-color);
  transform: translateX(2px);
}

/* No Results Styling */
.no-results {
  text-align: center;
  padding: 60px 20px;
  color: var(--muted-color);
}

.no-results i {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 16px;
  display: block;
}

.no-results h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 8px;
}

.no-results p {
  font-size: 1rem;
  opacity: 0.8;
  margin: 0;
}

/* Document Detail */
.document-detail {
  background: #ffffff;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 119, 255, 0.1);
}

.back-btn {
  display: flex;
  align-items: center;
  background: rgba(0, 119, 255, 0.1);
  border: 1px solid rgba(0, 119, 255, 0.2);
  color: var(--accent-color);
  padding: 8px 16px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.back-btn:hover {
  background: var(--accent-color);
  color: white;
  transform: translateX(-2px);
}

.back-btn i {
  margin-right: 8px;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 16px;
}

.category-tag {
  background: var(--accent-color);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.update-time {
  font-size: 14px;
  color: var(--default-color);
  opacity: 0.6;
}

.detail-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 32px;
  line-height: 1.2;
}

.content-body {
  font-size: 16px;
  line-height: 1.8;
  color: var(--default-color);
}

.content-body h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 32px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(0, 119, 255, 0.1);
}

.content-body h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 24px 0 12px 0;
}

.content-body p {
  margin-bottom: 16px;
}

.content-body ul, .content-body ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.content-body li {
  margin-bottom: 8px;
}

.content-body code {
  background: rgba(0, 119, 255, 0.1);
  color: var(--accent-color);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.content-body pre {
  background: #f8fafc;
  border: 1px solid rgba(0, 119, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 16px 0;
}

.content-body pre code {
  background: none;
  color: var(--default-color);
  padding: 0;
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .help-sidebar {
    position: static;
    margin-bottom: 40px;
  }

  .category-item {
    min-height: 70px;
    padding: 16px 20px;
  }

  .category-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .category-icon img {
    width: 24px;
    height: 24px;
  }

  .category-title {
    font-size: 15px;
  }

  .document-card {
    flex-direction: column;
    align-items: flex-start;
    min-height: auto;
    padding: 20px;
  }

  .document-actions {
    margin-left: 0;
    margin-top: 12px;
    align-self: flex-end;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .help-hero {
    padding: 120px 0 60px 0;
  }

  .help-hero h1 {
    font-size: 2.5rem;
  }

  .search-box .form-control {
    height: 50px;
    font-size: 14px;
  }

  .search-box .search-btn {
    width: 38px;
    height: 38px;
  }

  .help-categories {
    padding: 40px 0;
  }

  .document-list {
    padding: 24px;
  }

  .category-item {
    min-height: 56px;
    padding: 12px 16px;
    margin-bottom: 6px;
  }

  .category-icon {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }

  .category-icon img {
    width: 16px;
    height: 16px;
  }

  .category-title {
    font-size: 13px;
  }

  .count-badge {
    font-size: 10px;
    padding: 2px 5px;
    min-width: 16px;
  }

  .document-card {
    padding: 16px 20px;
    min-height: 70px;
  }

  .document-card h4 {
    font-size: 1rem;
  }

  .document-card p {
    font-size: 12px;
  }

  .detail-content h1 {
    font-size: 1.75rem;
  }
}
