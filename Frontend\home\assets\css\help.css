/*--------------------------------------------------------------
# Help Center Styles
--------------------------------------------------------------*/

/* Help Hero Section */
.help-hero {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 140px 0 80px 0;
  position: relative;
}

.help-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59,130,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.5;
}

.help-hero .container {
  position: relative;
  z-index: 1;
}

.help-hero h1 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 20px;
  line-height: 1.2;
}

.help-hero p {
  font-size: 1.2rem;
  color: var(--default-color);
  margin-bottom: 40px;
  opacity: 0.8;
}

/* Search Box */
.search-box {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-box .form-control {
  height: 60px;
  padding: 0 60px 0 24px;
  font-size: 16px;
  border: 2px solid rgba(0, 119, 255, 0.1);
  border-radius: 30px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-box .form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 4px 20px rgba(0, 119, 255, 0.2);
  outline: none;
}

.search-box .search-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  border: none;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.search-box .search-btn:hover {
  background: var(--accent-hover);
  transform: translateY(-50%) scale(1.05);
}

/* Help Categories Section */
.help-categories {
  padding: 80px 0;
  background: #ffffff;
}

/* Sidebar */
.help-sidebar {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 30px;
  position: sticky;
  top: 120px;
  border: 1px solid rgba(0, 119, 255, 0.1);
}

.help-sidebar h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--accent-color);
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.category-item:hover {
  background: rgba(0, 119, 255, 0.05);
  border-color: rgba(0, 119, 255, 0.1);
  transform: translateX(4px);
}

.category-item.active {
  background: linear-gradient(135deg, rgba(0, 119, 255, 0.1), rgba(0, 119, 255, 0.05));
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.category-item i {
  font-size: 18px;
  margin-right: 12px;
  width: 20px;
  text-align: center;
}

.category-item span {
  font-weight: 500;
  font-size: 15px;
}

/* Document List */
.document-list {
  background: #ffffff;
}

.category-header {
  margin-bottom: 40px;
}

.category-header h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 12px;
}

.category-header p {
  font-size: 1.1rem;
  color: var(--default-color);
  opacity: 0.8;
  margin: 0;
}

.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.document-card {
  background: #ffffff;
  border: 1px solid rgba(0, 119, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.document-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.document-card:hover::before {
  transform: scaleX(1);
}

.document-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color);
}

.document-card .doc-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-bottom: 16px;
}

.document-card h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 12px;
  line-height: 1.4;
}

.document-card p {
  color: var(--default-color);
  opacity: 0.8;
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 1.5;
}

.document-card .doc-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--default-color);
  opacity: 0.6;
}

.document-card .doc-meta .update-time {
  font-style: italic;
}

.document-card .doc-meta .read-more {
  color: var(--accent-color);
  font-weight: 500;
  text-decoration: none;
}

/* Document Detail */
.document-detail {
  background: #ffffff;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 119, 255, 0.1);
}

.back-btn {
  display: flex;
  align-items: center;
  background: rgba(0, 119, 255, 0.1);
  border: 1px solid rgba(0, 119, 255, 0.2);
  color: var(--accent-color);
  padding: 8px 16px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.back-btn:hover {
  background: var(--accent-color);
  color: white;
  transform: translateX(-2px);
}

.back-btn i {
  margin-right: 8px;
}

.detail-meta {
  display: flex;
  align-items: center;
  gap: 16px;
}

.category-tag {
  background: var(--accent-color);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.update-time {
  font-size: 14px;
  color: var(--default-color);
  opacity: 0.6;
}

.detail-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 32px;
  line-height: 1.2;
}

.content-body {
  font-size: 16px;
  line-height: 1.8;
  color: var(--default-color);
}

.content-body h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 32px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid rgba(0, 119, 255, 0.1);
}

.content-body h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 24px 0 12px 0;
}

.content-body p {
  margin-bottom: 16px;
}

.content-body ul, .content-body ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.content-body li {
  margin-bottom: 8px;
}

.content-body code {
  background: rgba(0, 119, 255, 0.1);
  color: var(--accent-color);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.content-body pre {
  background: #f8fafc;
  border: 1px solid rgba(0, 119, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 16px 0;
}

.content-body pre code {
  background: none;
  color: var(--default-color);
  padding: 0;
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .help-sidebar {
    position: static;
    margin-bottom: 40px;
  }
  
  .documents-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .help-hero {
    padding: 120px 0 60px 0;
  }
  
  .help-hero h1 {
    font-size: 2.5rem;
  }
  
  .search-box .form-control {
    height: 50px;
    font-size: 14px;
  }
  
  .search-box .search-btn {
    width: 38px;
    height: 38px;
  }
  
  .help-categories {
    padding: 60px 0;
  }
  
  .detail-content h1 {
    font-size: 2rem;
  }
}
