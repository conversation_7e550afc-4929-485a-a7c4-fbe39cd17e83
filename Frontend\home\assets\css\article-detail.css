/*--------------------------------------------------------------
# Article Detail Page Styles
--------------------------------------------------------------*/

/* Article Breadcrumb */
.article-breadcrumb {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 119, 255, 0.1);
}

.article-breadcrumb .breadcrumb {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  margin: 0;
  box-shadow: none;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.article-breadcrumb .breadcrumb-item {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.article-breadcrumb .breadcrumb-item a {
  color: var(--default-color);
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  opacity: 0.7;
  padding: 4px 8px;
  border-radius: 6px;
  line-height: 1;
}

.article-breadcrumb .breadcrumb-item a:hover {
  color: var(--accent-color);
  opacity: 1;
  background: rgba(0, 119, 255, 0.05);
}

.article-breadcrumb .breadcrumb-item a i {
  margin-right: 6px;
  font-size: 14px;
}

.article-breadcrumb .breadcrumb-item.active {
  color: var(--heading-color);
  font-weight: 600;
  opacity: 1;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  line-height: 1;
}

.article-breadcrumb .breadcrumb-item + .breadcrumb-item::before {
  content: "/";
  color: rgba(0, 119, 255, 0.3);
  font-weight: 400;
  margin: 0 8px;
  display: flex;
  align-items: center;
  line-height: 1;
}

/* Article Detail Section */
.article-detail-section {
  padding: 40px 0 80px 0;
  background: #ffffff;
}

/* Article Content */
.article-content {
  background: #ffffff;
}

/* Article Header */
.article-header {
  margin-bottom: 40px;
  padding-bottom: 32px;
  border-bottom: 2px solid rgba(0, 119, 255, 0.1);
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 16px 0 24px 0;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(0, 119, 255, 0.1);
}

.category-badge {
  background: var(--accent-color);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reading-time,
.publish-date {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--default-color);
  opacity: 0.7;
}

.reading-time i,
.publish-date i {
  margin-right: 6px;
  font-size: 16px;
}

.article-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 16px;
  line-height: 1.2;
}

.article-summary p {
  font-size: 1.2rem;
  color: var(--default-color);
  opacity: 0.8;
  margin: 0;
  line-height: 1.6;
}

/* Article Body */
.article-body {
  font-size: 16px;
  line-height: 1.8;
  color: var(--default-color);
  margin-bottom: 48px;
}

.article-body h2 {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 40px 0 20px 0;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(0, 119, 255, 0.1);
  position: relative;
}

.article-body h2::before {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: var(--accent-color);
}

.article-body h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 32px 0 16px 0;
}

.article-body h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 24px 0 12px 0;
}

.article-body p {
  margin-bottom: 20px;
}

.article-body ul,
.article-body ol {
  margin-bottom: 20px;
  padding-left: 28px;
}

.article-body li {
  margin-bottom: 8px;
}

.article-body blockquote {
  background: rgba(0, 119, 255, 0.05);
  border-left: 4px solid var(--accent-color);
  padding: 20px 24px;
  margin: 24px 0;
  border-radius: 0 8px 8px 0;
  font-style: italic;
}

.article-body img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 20px 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Article Tags */
.article-tags {
  margin-bottom: 32px;
  padding-bottom: 32px;
  border-bottom: 1px solid rgba(0, 119, 255, 0.1);
}

.article-tags h6 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 16px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tags-list .tag {
  padding: 6px 12px;
  background: rgba(0, 119, 255, 0.1);
  color: var(--accent-color);
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.tags-list .tag:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-2px);
}

/* Article Footer */
.article-footer {
  border-top: 2px solid rgba(0, 119, 255, 0.1);
  padding-top: 32px;
}

/* Article Navigation */
.article-navigation {
  display: flex;
  justify-content: space-between;
  gap: 24px;
}

.nav-item {
  flex: 1;
  max-width: 45%;
}

.nav-item.prev {
  text-align: left;
}

.nav-item.next {
  text-align: right;
}

.nav-label {
  display: block;
  font-size: 12px;
  color: var(--default-color);
  opacity: 0.6;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-link {
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid rgba(0, 119, 255, 0.1);
  padding: 16px 20px;
  border-radius: 12px;
  text-decoration: none;
  color: var(--default-color);
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(0, 119, 255, 0.1);
  border-color: var(--accent-color);
  color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-item.prev .nav-link {
  justify-content: flex-start;
}

.nav-item.next .nav-link {
  justify-content: flex-end;
}

.nav-link i {
  font-size: 18px;
}

.nav-item.prev .nav-link i {
  margin-right: 12px;
}

.nav-item.next .nav-link i {
  margin-left: 12px;
}

.nav-title {
  font-weight: 500;
  line-height: 1.4;
}

/* Related Articles Section */
.related-articles-section,
.recommended-articles-section {
  margin-top: 48px;
  padding-top: 48px;
  border-top: 2px solid rgba(0, 119, 255, 0.1);
}

.related-articles-section .section-header,
.recommended-articles-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.related-articles-section h4,
.recommended-articles-section h4 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 0;
  position: relative;
  padding-left: 20px;
}

.related-articles-section h4::before,
.recommended-articles-section h4::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: var(--accent-color);
  border-radius: 2px;
}

.section-more-link {
  color: var(--accent-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  padding: 6px 12px;
  border-radius: 6px;
  border: 1px solid transparent;
}

.section-more-link:hover {
  color: var(--accent-hover);
  background: rgba(0, 119, 255, 0.05);
  border-color: rgba(0, 119, 255, 0.2);
  transform: translateX(2px);
}

.section-more-link i {
  margin-left: 4px;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.section-more-link:hover i {
  transform: translateX(2px);
}

/* Articles Grid */
.related-articles-grid,
.recommended-articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

/* Article Item */
.related-article-item,
.recommended-article-item {
  background: #ffffff;
  border: 1px solid rgba(0, 119, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.related-article-item::before,
.recommended-article-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
  border-radius: 16px 16px 0 0;
  transform: scaleX(0);
  transition: transform 0.3s ease;
  transform-origin: left center;
}

.related-article-item:hover::before,
.recommended-article-item:hover::before {
  transform: scaleX(1);
}

.related-article-item:hover,
.recommended-article-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color);
}

/* Article Image */
.related-article-image,
.recommended-article-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
}

.related-article-image img,
.recommended-article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.related-article-item:hover .related-article-image img,
.recommended-article-item:hover .recommended-article-image img {
  transform: scale(1.05);
}

/* Article Content */
.related-article-content,
.recommended-article-content {
  flex: 1;
}

.related-article-title,
.recommended-article-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 8px;
  line-height: 1.4;
}

.related-article-title a,
.recommended-article-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

.related-article-title a:hover,
.recommended-article-title a:hover {
  color: var(--accent-color);
}

.related-article-excerpt,
.recommended-article-excerpt {
  color: var(--default-color);
  opacity: 0.8;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-article-meta,
.recommended-article-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--default-color);
  opacity: 0.6;
}

.related-article-meta i,
.recommended-article-meta i {
  margin-right: 4px;
}

/* No Image Layout */
.related-article-item.no-image,
.recommended-article-item.no-image {
  padding: 16px 20px;
}

.related-article-item.no-image .related-article-content,
.recommended-article-item.no-image .recommended-article-content {
  width: 100%;
}

.related-article-item.no-image .related-article-excerpt,
.recommended-article-item.no-image .recommended-article-excerpt {
  -webkit-line-clamp: 3;
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .article-sidebar {
    position: static;
    margin-bottom: 40px;
  }

  .article-navigation {
    flex-direction: column;
  }

  .nav-item {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .breadcrumb-section {
    padding: 100px 0 30px 0;
  }

  .article-title {
    font-size: 2rem;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .article-actions {
    flex-wrap: wrap;
  }

  .action-btn {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .comment-item {
    flex-direction: column;
  }

  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
