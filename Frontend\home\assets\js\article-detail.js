/**
 * Article Detail Page JavaScript
 */

// 文章详细数据
const articleDetailData = {
  1: {
    title: "如何提高数字商品转化率：从流量到成交的完整指南",
    category: "operation",
    categoryName: "运营技巧",
    publishDate: "2024-01-15",
    readTime: 8,
    views: 1234,
    likes: 128,
    summary:
      "在数字商品销售中，转化率是衡量成功的关键指标。本文将分享提高转化率的实用技巧和策略，帮助您将更多访客转化为付费客户。",
    featuredImage: "https://via.placeholder.com/800x400",
    tags: ["转化率", "数字商品", "营销技巧", "用户体验", "A/B测试"],
    author: {
      name: "法宝团队",
      bio: "专注于电商自动化解决方案，帮助商家提升运营效率",
      avatar: "https://via.placeholder.com/60x60",
    },
    content: `
      <h2>什么是转化率？</h2>
      <p>转化率是指访问您网站或店铺的用户中，实际完成购买行为的用户比例。对于数字商品销售来说，转化率直接影响着您的收入和业务增长。</p>
      
      <blockquote>
        <p>一个1%的转化率提升，可能意味着数万元的额外收入。</p>
      </blockquote>
      
      <h2>影响转化率的关键因素</h2>
      
      <h3>1. 产品页面优化</h3>
      <p>产品页面是用户做出购买决策的关键页面，需要重点优化：</p>
      <ul>
        <li><strong>清晰的产品描述</strong>：详细说明产品功能、使用方法和价值</li>
        <li><strong>高质量的产品图片</strong>：展示产品的实际效果和使用场景</li>
        <li><strong>用户评价和反馈</strong>：真实的用户评价能够建立信任</li>
        <li><strong>明确的价格信息</strong>：避免隐藏费用，提供透明的定价</li>
      </ul>
      
      <h3>2. 用户体验优化</h3>
      <p>良好的用户体验是提高转化率的基础：</p>
      <ul>
        <li>页面加载速度要快（建议3秒内）</li>
        <li>购买流程要简单明了</li>
        <li>支持多种支付方式</li>
        <li>提供优质的客户服务</li>
      </ul>
      
      <h2>实用的转化率优化技巧</h2>
      
      <h3>A/B测试</h3>
      <p>通过A/B测试来验证不同版本的效果：</p>
      <ol>
        <li>确定测试目标和假设</li>
        <li>设计不同版本的页面</li>
        <li>随机分配流量</li>
        <li>收集和分析数据</li>
        <li>实施效果更好的版本</li>
      </ol>
      
      <h3>社会证明</h3>
      <p>利用社会证明来增强用户信心：</p>
      <ul>
        <li>展示销售数量和用户数量</li>
        <li>显示用户评价和推荐</li>
        <li>添加权威机构认证</li>
        <li>展示媒体报道和奖项</li>
      </ul>
      
      <h2>数据分析与持续优化</h2>
      <p>转化率优化是一个持续的过程，需要：</p>
      <ul>
        <li>定期分析转化漏斗</li>
        <li>识别流失点和优化机会</li>
        <li>跟踪关键指标的变化</li>
        <li>根据数据调整策略</li>
      </ul>
      
      <h2>总结</h2>
      <p>提高数字商品转化率需要从多个维度进行优化，包括产品页面、用户体验、营销策略等。通过持续的测试和优化，您可以显著提升转化率，实现业务增长。</p>
    `,
  },
}

// 从URL获取文章ID
function getArticleIdFromURL() {
  const urlParams = new URLSearchParams(window.location.search)
  return parseInt(urlParams.get("id")) || 1
}

// 计算阅读时间
function calculateReadingTime(content) {
  const wordsPerMinute = 200
  const textContent = content.replace(/<[^>]*>/g, "")
  const wordCount = textContent.length
  const readingTime = Math.ceil(wordCount / wordsPerMinute / 10)
  return Math.max(1, readingTime)
}

// 加载相关文章
function loadRelatedArticles(currentArticleId) {
  const relatedContainer = document.getElementById("relatedArticlesGrid")

  // 模拟相关文章数据
  const relatedArticles = [
    {
      id: 2,
      title: "电商平台对接完整指南",
      excerpt:
        "详细介绍如何将您的数字商品店铺与主流电商平台对接，实现多平台统一管理。",
      date: "2024-01-12",
      readTime: 12,
      views: 2156,
      image: "https://via.placeholder.com/160x160",
      hasImage: true,
    },
    {
      id: 3,
      title: "自动发货系统优化技巧",
      excerpt: "分享自动发货系统的优化方法，包括库存管理、订单处理等最佳实践。",
      date: "2024-01-10",
      readTime: 6,
      views: 987,
      image: null,
      hasImage: false,
    },
    {
      id: 4,
      title: "数字商品行业趋势分析",
      excerpt:
        "深入分析数字商品行业的发展趋势，包括市场规模、用户行为等多个维度。",
      date: "2024-01-08",
      readTime: 15,
      views: 3421,
      image: "https://via.placeholder.com/160x160",
      hasImage: true,
    },
  ]

  relatedContainer.innerHTML = relatedArticles
    .map(
      (article) => `
    <div class="related-article-item ${article.hasImage ? "" : "no-image"}">
      ${
        article.hasImage
          ? `
        <div class="related-article-image">
          <img src="${article.image}" alt="${article.title}" />
        </div>
      `
          : ""
      }
      <div class="related-article-content">
        <h5 class="related-article-title">
          <a href="article-detail.html?id=${article.id}">${article.title}</a>
        </h5>
        <p class="related-article-excerpt">${article.excerpt}</p>
        <div class="related-article-meta">
          <span><i class="bi bi-calendar-check"></i>${article.date}</span>
          <span><i class="bi bi-clock"></i>${article.readTime} 分钟</span>
          <span><i class="bi bi-eye"></i>${article.views.toLocaleString()}</span>
        </div>
      </div>
    </div>
  `
    )
    .join("")
}

// 加载推荐文章
function loadRecommendedArticles() {
  const recommendedContainer = document.getElementById(
    "recommendedArticlesGrid"
  )

  // 模拟推荐文章数据
  const recommendedArticles = [
    {
      id: 5,
      title: "成功案例：某游戏工作室月销售额突破100万",
      excerpt:
        "分享一个游戏工作室如何通过法宝平台实现月销售额突破100万的成功经验。",
      date: "2024-01-05",
      readTime: 10,
      views: 5678,
      image: null,
      hasImage: false,
    },
    {
      id: 6,
      title: "抖音小店数字商品运营策略",
      excerpt:
        "详细介绍在抖音小店销售数字商品的运营策略，包括内容创作、直播带货等。",
      date: "2024-01-03",
      readTime: 9,
      views: 2345,
      image: "https://via.placeholder.com/160x160",
      hasImage: true,
    },
    {
      id: 7,
      title: "微信小程序商城开发实战",
      excerpt:
        "从零开始构建微信小程序商城，包括界面设计、支付集成等核心功能实现。",
      date: "2024-01-01",
      readTime: 20,
      views: 1876,
      image: "https://via.placeholder.com/160x160",
      hasImage: true,
    },
  ]

  recommendedContainer.innerHTML = recommendedArticles
    .map(
      (article) => `
    <div class="recommended-article-item ${article.hasImage ? "" : "no-image"}">
      ${
        article.hasImage
          ? `
        <div class="recommended-article-image">
          <img src="${article.image}" alt="${article.title}" />
        </div>
      `
          : ""
      }
      <div class="recommended-article-content">
        <h5 class="recommended-article-title">
          <a href="article-detail.html?id=${article.id}">${article.title}</a>
        </h5>
        <p class="recommended-article-excerpt">${article.excerpt}</p>
        <div class="recommended-article-meta">
          <span><i class="bi bi-calendar-check"></i>${article.date}</span>
          <span><i class="bi bi-clock"></i>${article.readTime} 分钟</span>
          <span><i class="bi bi-eye"></i>${article.views.toLocaleString()}</span>
        </div>
      </div>
    </div>
  `
    )
    .join("")
}

// 加载文章详情
function loadArticleDetail() {
  const articleId = getArticleIdFromURL()
  const article = articleDetailData[articleId]

  if (!article) {
    console.error("Article not found")
    return
  }

  // 更新页面标题
  document.title = `${article.title} - 法宝`
  document.getElementById("pageTitle").textContent = `${article.title} - 法宝`

  // 更新面包屑
  document.getElementById("breadcrumbCategory").textContent =
    article.categoryName
  document.getElementById("breadcrumbTitle").textContent = article.title

  // 更新文章信息
  document.getElementById("articleCategory").textContent = article.categoryName
  document.getElementById("articleTitle").textContent = article.title
  document.getElementById("articleSummary").textContent = article.summary
  document.getElementById("publishDate").textContent = article.publishDate

  // 计算并显示阅读时间
  const readingTime = calculateReadingTime(article.content)
  document.getElementById("readingTime").textContent = readingTime

  // 加载文章内容
  document.getElementById("articleBody").innerHTML = article.content

  // 显示标签
  const tagsContainer = document.getElementById("articleTags")
  tagsContainer.innerHTML = article.tags
    .map((tag) => `<a href="#" class="tag">${tag}</a>`)
    .join("")

  // 加载相关文章和推荐文章
  loadRelatedArticles(articleId)
  loadRecommendedArticles()
}

// 点赞功能
function toggleLike() {
  const likeIcon = document.getElementById("likeIcon")
  const likeText = document.getElementById("likeText")
  const likeCount = document.getElementById("likeCount")
  const actionBtn = likeIcon.closest(".action-btn")

  if (actionBtn.classList.contains("liked")) {
    actionBtn.classList.remove("liked")
    likeIcon.className = "bi bi-heart"
    likeText.textContent = "点赞"
    // 这里可以发送取消点赞请求到服务器
  } else {
    actionBtn.classList.add("liked")
    likeIcon.className = "bi bi-heart-fill"
    likeText.textContent = "已点赞"
    // 这里可以发送点赞请求到服务器
  }
}

// 收藏功能
function toggleBookmark() {
  const bookmarkIcon = document.getElementById("bookmarkIcon")
  const bookmarkText = document.getElementById("bookmarkText")
  const actionBtn = bookmarkIcon.closest(".action-btn")

  if (actionBtn.classList.contains("bookmarked")) {
    actionBtn.classList.remove("bookmarked")
    bookmarkIcon.className = "bi bi-bookmark"
    bookmarkText.textContent = "收藏"
  } else {
    actionBtn.classList.add("bookmarked")
    bookmarkIcon.className = "bi bi-bookmark-fill"
    bookmarkText.textContent = "已收藏"
  }
}

// 分享功能
function shareArticle() {
  if (navigator.share) {
    navigator.share({
      title: document.getElementById("articleTitle").textContent,
      text: document.getElementById("articleSummary").textContent,
      url: window.location.href,
    })
  } else {
    copyLink()
  }
}

// 分享到微信
function shareToWeChat() {
  alert("请复制链接后在微信中分享")
  copyLink()
}

// 分享到微博
function shareToWeibo() {
  const title = document.getElementById("articleTitle").textContent
  const url = encodeURIComponent(window.location.href)
  const text = encodeURIComponent(`${title} - 法宝`)
  window.open(
    `https://service.weibo.com/share/share.php?url=${url}&title=${text}`
  )
}

// 复制链接
function copyLink() {
  navigator.clipboard.writeText(window.location.href).then(() => {
    alert("链接已复制到剪贴板")
  })
}

// 初始化
document.addEventListener("DOMContentLoaded", function () {
  // 初始化AOS动画
  AOS.init({
    duration: 800,
    easing: "ease-in-out",
    once: true,
    mirror: false,
  })

  // 加载文章详情
  loadArticleDetail()
})
