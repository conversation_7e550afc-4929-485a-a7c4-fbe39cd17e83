/**
 * Help Center JavaScript
 */

// 文档数据
const documentsData = {
  "getting-started": {
    title: "快速开始",
    description: "帮助您快速了解和使用法宝平台",
    documents: [
      {
        id: "quick-start",
        title: "快速入门指南",
        description: "5分钟快速了解法宝平台的核心功能和使用方法",
        icon: "bi-rocket-takeoff",
        updateTime: "2024-01-15",
        content: `
          <h2>欢迎使用法宝平台</h2>
          <p>法宝是一个专业的数字产品自动发货平台，为电商商家提供高效、安全的数字商品交付解决方案。</p>

          <h3>第一步：注册账号</h3>
          <p>访问 <code>https://console.fabao.com</code> 注册您的法宝账号。</p>
          <ul>
            <li>填写基本信息</li>
            <li>验证邮箱地址</li>
            <li>完成实名认证</li>
          </ul>

          <h3>第二步：创建商品</h3>
          <p>在控制台中创建您的第一个数字商品：</p>
          <ol>
            <li>点击"商品管理" → "添加商品"</li>
            <li>填写商品基本信息</li>
            <li>上传商品文件或设置卡密</li>
            <li>配置发货规则</li>
          </ol>

          <h3>第三步：对接平台</h3>
          <p>将您的店铺与法宝平台对接：</p>
          <ul>
            <li>选择对应的电商平台</li>
            <li>授权店铺访问权限</li>
            <li>配置商品映射关系</li>
          </ul>

          <h2>常用功能</h2>
          <h3>订单管理</h3>
          <p>实时查看订单状态，支持批量处理和异常订单处理。</p>

          <h3>数据统计</h3>
          <p>详细的销售数据分析，帮助您优化经营策略。</p>
        `,
      },
      {
        id: "account-setup",
        title: "账号设置与认证",
        description: "完成账号设置和实名认证，确保平台功能正常使用",
        icon: "bi-person-gear",
        updateTime: "2024-01-12",
        content: `
          <h2>账号设置</h2>
          <p>完善的账号设置是使用法宝平台的基础，请按照以下步骤完成设置。</p>

          <h3>基本信息设置</h3>
          <ul>
            <li>头像上传</li>
            <li>昵称设置</li>
            <li>联系方式绑定</li>
          </ul>

          <h3>实名认证</h3>
          <p>为了保障交易安全，平台要求完成实名认证：</p>
          <ol>
            <li>上传身份证正反面照片</li>
            <li>填写真实姓名和身份证号</li>
            <li>等待系统审核（通常1-3个工作日）</li>
          </ol>

          <h3>安全设置</h3>
          <ul>
            <li>设置安全密码</li>
            <li>绑定手机号码</li>
            <li>开启二次验证</li>
          </ul>
        `,
      },
    ],
  },
  "platform-guide": {
    title: "平台对接",
    description: "详细的电商平台对接指南",
    documents: [
      {
        id: "taobao-integration",
        title: "淘宝/天猫对接指南",
        description: "详细介绍如何将淘宝或天猫店铺与法宝平台对接",
        icon: "bi-shop",
        updateTime: "2024-01-10",
        content: `
          <h2>淘宝/天猫对接</h2>
          <p>本指南将帮助您完成淘宝或天猫店铺与法宝平台的对接。</p>

          <h3>准备工作</h3>
          <ul>
            <li>确保您拥有淘宝/天猫店铺的管理权限</li>
            <li>准备店铺的基本信息</li>
            <li>确认要对接的商品清单</li>
          </ul>

          <h3>对接步骤</h3>
          <ol>
            <li>登录法宝控制台</li>
            <li>进入"平台管理" → "添加平台"</li>
            <li>选择"淘宝/天猫"</li>
            <li>点击"授权登录"</li>
            <li>在弹出的淘宝页面完成授权</li>
            <li>返回法宝平台完成配置</li>
          </ol>

          <h3>商品映射</h3>
          <p>对接完成后，需要建立平台商品与法宝商品的映射关系：</p>
          <ul>
            <li>自动匹配：系统根据商品标题自动匹配</li>
            <li>手动映射：手动选择对应的法宝商品</li>
            <li>批量操作：支持批量设置映射关系</li>
          </ul>
        `,
      },
    ],
  },
  "product-management": {
    title: "商品管理",
    description: "商品创建、编辑和管理的详细说明",
    documents: [
      {
        id: "create-product",
        title: "创建数字商品",
        description: "学习如何在法宝平台创建和配置数字商品",
        icon: "bi-plus-circle",
        updateTime: "2024-01-08",
        content: `
          <h2>创建数字商品</h2>
          <p>数字商品是法宝平台的核心功能，支持多种类型的数字产品。</p>

          <h3>商品类型</h3>
          <ul>
            <li><strong>文件类商品</strong>：软件、文档、图片等文件</li>
            <li><strong>卡密类商品</strong>：游戏点卡、会员卡等</li>
            <li><strong>文本类商品</strong>：激活码、序列号等</li>
          </ul>

          <h3>创建步骤</h3>
          <ol>
            <li>进入"商品管理" → "添加商品"</li>
            <li>选择商品类型</li>
            <li>填写基本信息：
              <ul>
                <li>商品名称</li>
                <li>商品描述</li>
                <li>商品分类</li>
                <li>售价设置</li>
              </ul>
            </li>
            <li>上传商品内容</li>
            <li>设置发货规则</li>
            <li>保存并发布</li>
          </ol>

          <h3>发货规则配置</h3>
          <p>灵活的发货规则确保商品正确交付：</p>
          <ul>
            <li>立即发货：订单确认后立即发货</li>
            <li>延时发货：设置延时时间</li>
            <li>条件发货：根据特定条件触发发货</li>
          </ul>
        `,
      },
    ],
  },
  "order-management": {
    title: "订单管理",
    description: "订单处理、状态跟踪和异常处理",
    documents: [
      {
        id: "order-processing",
        title: "订单处理流程",
        description: "了解订单从接收到完成的完整处理流程",
        icon: "bi-arrow-repeat",
        updateTime: "2024-01-05",
        content: `
          <h2>订单处理流程</h2>
          <p>法宝平台提供完整的订单处理流程，确保每个订单都能正确处理。</p>

          <h3>订单状态</h3>
          <ul>
            <li><strong>待处理</strong>：新接收的订单</li>
            <li><strong>处理中</strong>：正在准备发货</li>
            <li><strong>已发货</strong>：商品已发送给买家</li>
            <li><strong>已完成</strong>：订单处理完成</li>
            <li><strong>异常</strong>：需要人工处理的订单</li>
          </ul>

          <h3>自动处理</h3>
          <p>大部分订单可以实现自动处理：</p>
          <ol>
            <li>系统接收订单</li>
            <li>验证订单信息</li>
            <li>匹配商品库存</li>
            <li>自动发货</li>
            <li>更新订单状态</li>
          </ol>

          <h3>异常处理</h3>
          <p>遇到异常订单时的处理方法：</p>
          <ul>
            <li>库存不足：及时补充库存</li>
            <li>商品映射错误：重新设置映射关系</li>
            <li>买家信息异常：联系买家确认</li>
          </ul>
        `,
      },
    ],
  },
  "api-docs": {
    title: "API文档",
    description: "开发者API接口文档和示例",
    documents: [
      {
        id: "api-overview",
        title: "API概览",
        description: "法宝平台API的基本介绍和认证方式",
        icon: "bi-code-square",
        updateTime: "2024-01-03",
        content: `
          <h2>API概览</h2>
          <p>法宝平台提供RESTful API，支持开发者集成和自定义功能。</p>

          <h3>基础信息</h3>
          <ul>
            <li><strong>API地址</strong>：<code>https://api.fabao.com</code></li>
            <li><strong>协议</strong>：HTTPS</li>
            <li><strong>格式</strong>：JSON</li>
            <li><strong>编码</strong>：UTF-8</li>
          </ul>

          <h3>认证方式</h3>
          <p>API使用API Key进行认证：</p>
          <pre><code>Authorization: Bearer YOUR_API_KEY</code></pre>

          <h3>请求示例</h3>
          <pre><code>curl -X GET "https://api.fabao.com/v1/orders" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"</code></pre>

          <h3>响应格式</h3>
          <pre><code>{
  "code": 200,
  "message": "success",
  "data": {
    // 响应数据
  }
}</code></pre>

          <h3>错误处理</h3>
          <p>API错误响应格式：</p>
          <pre><code>{
  "code": 400,
  "message": "参数错误",
  "error": "详细错误信息"
}</code></pre>
        `,
      },
    ],
  },
  troubleshooting: {
    title: "故障排除",
    description: "常见问题的解决方案和故障排除指南",
    documents: [
      {
        id: "common-issues",
        title: "常见问题解决",
        description: "平台使用过程中常见问题的解决方案",
        icon: "bi-question-circle",
        updateTime: "2024-01-01",
        content: `
          <h2>常见问题解决</h2>
          <p>本文档收集了用户在使用过程中遇到的常见问题及解决方案。</p>

          <h3>登录问题</h3>
          <h4>Q: 忘记密码怎么办？</h4>
          <p>A: 在登录页面点击"忘记密码"，输入注册邮箱，系统会发送重置密码邮件。</p>

          <h4>Q: 账号被锁定怎么办？</h4>
          <p>A: 连续输错密码会导致账号临时锁定，请等待30分钟后重试，或联系客服解锁。</p>

          <h3>发货问题</h3>
          <h4>Q: 为什么订单没有自动发货？</h4>
          <p>A: 可能的原因：</p>
          <ul>
            <li>商品库存不足</li>
            <li>商品映射关系未设置</li>
            <li>发货规则配置错误</li>
            <li>订单信息异常</li>
          </ul>

          <h4>Q: 如何处理发货失败的订单？</h4>
          <p>A: 进入订单管理页面，找到失败订单，查看失败原因，根据提示进行处理。</p>

          <h3>平台对接问题</h3>
          <h4>Q: 授权失败怎么办？</h4>
          <p>A: 检查以下几点：</p>
          <ul>
            <li>确认店铺账号权限</li>
            <li>检查网络连接</li>
            <li>清除浏览器缓存</li>
            <li>重新进行授权</li>
          </ul>
        `,
      },
    ],
  },
}

// 当前选中的分类
let currentCategory = "getting-started"

// 初始化
document.addEventListener("DOMContentLoaded", function () {
  // 初始化AOS动画
  AOS.init({
    duration: 800,
    easing: "ease-in-out",
    once: true,
    mirror: false,
  })

  // 更新分类文档数量
  updateCategoryDocumentCounts()

  // 加载默认分类
  loadCategory(currentCategory)

  // 绑定分类点击事件
  bindCategoryEvents()

  // 绑定搜索事件
  bindSearchEvents()
})

// 更新分类文档数量
function updateCategoryDocumentCounts() {
  Object.keys(documentsData).forEach((categoryKey) => {
    const category = documentsData[categoryKey]
    const categoryItem = document.querySelector(
      `[data-category="${categoryKey}"]`
    )
    if (categoryItem) {
      const countBadge = categoryItem.querySelector(".count-badge")
      if (countBadge) {
        countBadge.textContent = category.documents.length
      }
    }
  })
}

// 绑定分类点击事件
function bindCategoryEvents() {
  const categoryItems = document.querySelectorAll(".category-item")
  categoryItems.forEach((item) => {
    item.addEventListener("click", function () {
      const category = this.dataset.category
      if (category !== currentCategory) {
        // 更新选中状态
        categoryItems.forEach((i) => i.classList.remove("active"))
        this.classList.add("active")

        // 加载新分类
        currentCategory = category
        loadCategory(category)

        // 显示文档列表
        showDocumentList()
      }
    })
  })
}

// 绑定搜索事件
function bindSearchEvents() {
  const searchInput = document.getElementById("searchInput")
  const searchBtn = document.querySelector(".search-btn")

  // 搜索按钮点击事件
  searchBtn.addEventListener("click", performSearch)

  // 回车搜索
  searchInput.addEventListener("keypress", function (e) {
    if (e.key === "Enter") {
      performSearch()
    }
  })
}

// 执行搜索
function performSearch() {
  const keyword = document.getElementById("searchInput").value.trim()
  if (keyword) {
    searchDocuments(keyword)
  }
}

// 搜索文档
function searchDocuments(keyword) {
  const results = []

  // 在所有分类中搜索
  Object.keys(documentsData).forEach((categoryKey) => {
    const category = documentsData[categoryKey]
    category.documents.forEach((doc) => {
      if (
        doc.title.toLowerCase().includes(keyword.toLowerCase()) ||
        doc.description.toLowerCase().includes(keyword.toLowerCase()) ||
        doc.content.toLowerCase().includes(keyword.toLowerCase())
      ) {
        results.push({
          ...doc,
          category: categoryKey,
          categoryTitle: category.title,
        })
      }
    })
  })

  // 显示搜索结果
  displaySearchResults(results, keyword)
}

// 显示搜索结果
function displaySearchResults(results, keyword) {
  const categoryTitle = document.getElementById("categoryTitle")
  const categoryDescription = document.getElementById("categoryDescription")
  const documentsGrid = document.querySelector(".documents-grid")

  categoryTitle.textContent = `搜索结果：${keyword}`
  categoryDescription.textContent = `找到 ${results.length} 个相关文档`

  if (results.length === 0) {
    documentsGrid.innerHTML = `
      <div class="col-12 text-center">
        <div class="no-results">
          <i class="bi bi-search" style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
          <h4>未找到相关文档</h4>
          <p>请尝试其他关键词或浏览分类文档</p>
        </div>
      </div>
    `
    return
  }

  documentsGrid.innerHTML = results
    .map(
      (doc) => `
    <div class="document-card" onclick="showDocumentDetail('${doc.category}', '${doc.id}')">
      <div class="document-content">
        <h4>${doc.title}</h4>
        <p>${doc.description}</p>
        <div class="doc-meta">
          <span class="category-tag">${doc.categoryTitle}</span>
          <span class="update-time">更新于 ${doc.updateTime}</span>
        </div>
      </div>
      <div class="document-actions">
        <i class="bi bi-chevron-right doc-arrow"></i>
      </div>
    </div>
  `
    )
    .join("")
}

// 加载分类
function loadCategory(categoryKey) {
  const category = documentsData[categoryKey]
  if (!category) return

  // 更新标题和描述
  document.getElementById("categoryTitle").textContent = category.title
  document.getElementById("categoryDescription").textContent =
    category.description

  // 渲染文档列表
  const documentsGrid = document.querySelector(".documents-grid")
  documentsGrid.innerHTML = category.documents
    .map(
      (doc) => `
    <div class="document-card" onclick="showDocumentDetail('${categoryKey}', '${doc.id}')">
      <div class="document-content">
        <h4>${doc.title}</h4>
        <p>${doc.description}</p>
        <div class="doc-meta">
          <span class="update-time">更新于 ${doc.updateTime}</span>
        </div>
      </div>
      <div class="document-actions">
        <i class="bi bi-chevron-right doc-arrow"></i>
      </div>
    </div>
  `
    )
    .join("")
}

// 显示文档详情（跳转到详情页）
function showDocumentDetail(categoryKey, docId) {
  // 跳转到独立的详情页
  window.location.href = `help-detail.html?category=${categoryKey}&doc=${docId}`
}
