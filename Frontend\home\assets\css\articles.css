/*--------------------------------------------------------------
# Articles Page Styles
--------------------------------------------------------------*/

/* Articles Carousel Section */
.articles-carousel {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 120px 0 60px 0;
  position: relative;
}

.articles-carousel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59,130,246,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.articles-carousel .container {
  position: relative;
  z-index: 1;
}

/* Carousel Content */
.carousel-content {
  padding: 40px 0;
}

.carousel-text h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--heading-color);
  margin-bottom: 20px;
  line-height: 1.2;
}

.carousel-text p {
  font-size: 1.2rem;
  color: var(--default-color);
  opacity: 0.8;
  margin-bottom: 30px;
  line-height: 1.6;
}

.carousel-text .btn {
  background: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
  padding: 12px 32px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.carousel-text .btn:hover {
  background: var(--accent-hover);
  border-color: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 119, 255, 0.3);
}

.carousel-image {
  text-align: center;
}

.carousel-image img {
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.carousel-image img:hover {
  transform: scale(1.02);
}

/* Carousel Controls */
.carousel-control-prev,
.carousel-control-next {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.8;
  transition: all 0.3s ease;
}

.carousel-control-prev {
  left: -25px;
}

.carousel-control-next {
  right: -25px;
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  opacity: 1;
  background: var(--accent-color);
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 20px;
  height: 20px;
}

/* Carousel Indicators */
.carousel-indicators {
  bottom: -50px;
}

.carousel-indicators [data-bs-target] {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(0, 119, 255, 0.3);
  border: none;
  margin: 0 6px;
  transition: all 0.3s ease;
}

.carousel-indicators .active {
  background-color: var(--accent-color);
  transform: scale(1.2);
}

/* Articles List Section */

/* Articles List Section */
.articles-list-section {
  background: #ffffff;
  padding: 60px 0;
}

/* Articles List */
.articles-list {
  display: flex;
  flex-direction: column;
  gap: 32px;
  margin-bottom: 48px;
}

/* Article Item */
.article-item {
  background: #ffffff;
  border: 1px solid rgba(0, 119, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  gap: 32px;
  align-items: flex-start;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.article-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
  border-color: var(--accent-color);
}

/* Article Image */
.article-item-image {
  flex-shrink: 0;
  width: 200px;
  height: 140px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.article-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.article-item:hover .article-item-image img {
  transform: scale(1.05);
}

.article-item-image .category-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: var(--accent-color);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Article Content */
.article-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 140px;
}

.article-item-header {
  margin-bottom: 12px;
}

.article-item-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 8px;
  line-height: 1.4;
}

.article-item-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

.article-item-title a:hover {
  color: var(--accent-color);
}

.article-item-excerpt {
  color: var(--default-color);
  opacity: 0.8;
  line-height: 1.6;
  margin-bottom: 16px;
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.article-item-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.article-item-tag {
  padding: 4px 10px;
  background: rgba(0, 119, 255, 0.1);
  color: var(--accent-color);
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.article-item-tag:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-2px);
}

.article-item-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* No Image Layout */
.article-item.no-image {
  padding: 24px 32px;
}

.article-item.no-image .article-item-content {
  height: auto;
}

.article-item.no-image .article-item-title {
  font-size: 1.4rem;
}

.article-item.no-image .article-item-excerpt {
  -webkit-line-clamp: 3;
}

/* Article Image */
.article-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
  transform: scale(1.05);
}

.article-category {
  position: absolute;
  top: 16px;
  left: 16px;
  background: var(--accent-color);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Article Content */
.article-content {
  padding: 24px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.article-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 12px;
  line-height: 1.4;
}

.article-content h3 a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

.article-content h3 a:hover {
  color: var(--accent-color);
}

.article-excerpt {
  color: var(--default-color);
  opacity: 0.8;
  margin-bottom: 16px;
  line-height: 1.6;
  flex-grow: 1;
}

/* Article Meta */
.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: var(--default-color);
  opacity: 0.7;
  margin-bottom: 16px;
}

.article-date {
  display: flex;
  align-items: center;
}

.article-date i {
  margin-right: 6px;
}

.article-stats {
  display: flex;
  gap: 16px;
}

.article-stat {
  display: flex;
  align-items: center;
}

.article-stat i {
  margin-right: 4px;
}

/* Article Actions */
.article-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.read-more {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.read-more:hover {
  color: var(--accent-hover);
  transform: translateX(4px);
}

.read-more i {
  margin-left: 6px;
  transition: transform 0.3s ease;
}

.read-more:hover i {
  transform: translateX(4px);
}

.article-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.article-tag {
  padding: 2px 8px;
  background: rgba(0, 119, 255, 0.1);
  color: var(--accent-color);
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
  text-decoration: none;
}

/* Pagination */
.articles-pagination {
  margin-top: 48px;
  margin-bottom: 24px;
}

.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.5rem;
  --bs-pagination-font-size: 1rem;
  --bs-pagination-color: var(--default-color);
  --bs-pagination-bg: #ffffff;
  --bs-pagination-border-width: 1px;
  --bs-pagination-border-color: rgba(0, 119, 255, 0.1);
  --bs-pagination-border-radius: 8px;
  --bs-pagination-hover-color: var(--accent-color);
  --bs-pagination-hover-bg: rgba(0, 119, 255, 0.1);
  --bs-pagination-hover-border-color: var(--accent-color);
  --bs-pagination-focus-color: var(--accent-color);
  --bs-pagination-focus-bg: rgba(0, 119, 255, 0.1);
  --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(0, 119, 255, 0.25);
  --bs-pagination-active-color: #ffffff;
  --bs-pagination-active-bg: var(--accent-color);
  --bs-pagination-active-border-color: var(--accent-color);
  --bs-pagination-disabled-color: rgba(0, 0, 0, 0.3);
  --bs-pagination-disabled-bg: #ffffff;
  --bs-pagination-disabled-border-color: rgba(0, 119, 255, 0.1);
}

.pagination .page-link {
  margin: 0 2px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 44px;
  text-align: center;
}

.pagination .page-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 119, 255, 0.2);
}

.pagination .page-item.active .page-link {
  box-shadow: 0 4px 12px rgba(0, 119, 255, 0.3);
}

.pagination .page-item.disabled .page-link {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Articles Info */
.articles-info {
  margin-top: 16px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(0, 119, 255, 0.1);
}

.articles-info p {
  margin: 0;
  font-size: 14px;
}

.articles-info span {
  font-weight: 600;
  color: var(--accent-color);
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .articles-sidebar {
    position: static;
    margin-bottom: 40px;
  }

  .articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;
  }

  .filter-tabs {
    gap: 12px;
  }

  .filter-tab {
    padding: 10px 16px;
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .articles-hero {
    padding: 120px 0 60px 0;
  }

  .articles-hero h1 {
    font-size: 2.5rem;
  }

  .search-box .form-control {
    height: 50px;
    font-size: 14px;
  }

  .search-box .search-btn {
    width: 38px;
    height: 38px;
  }

  .articles-grid {
    grid-template-columns: 1fr;
  }

  .filter-tabs {
    flex-direction: column;
    align-items: center;
  }

  .filter-tab {
    width: 200px;
    justify-content: center;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .article-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
