/*--------------------------------------------------------------
# Help Search Results Page Styles
--------------------------------------------------------------*/

/* Compact Search Hero Section */
.search-hero {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  padding: 120px 0 40px 0;
  position: relative;
  border-bottom: 1px solid var(--border-color);
}

.search-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(59,130,246,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.6;
}

.search-hero .container {
  position: relative;
  z-index: 1;
}

.search-header {
  text-align: center;
  margin-bottom: 32px;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: var(--accent-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.back-link:hover {
  color: var(--accent-hover);
  transform: translateX(-2px);
}

.search-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 0;
  line-height: 1.3;
}

/* Compact Search Box */
.search-hero .search-box {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.search-hero .search-box .form-control {
  height: 56px;
  padding: 0 56px 0 20px;
  font-size: 16px;
  border: 2px solid rgba(0, 119, 255, 0.1);
  border-radius: 28px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.search-hero .search-box .form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 4px 20px rgba(0, 119, 255, 0.15);
  outline: none;
}

.search-hero .search-box .search-btn {
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  border: none;
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.search-hero .search-box .search-btn:hover {
  background: var(--accent-hover);
  transform: translateY(-50%) scale(1.05);
}

/* Search Results Section */
.search-results {
  padding: 60px 0;
  background: #fafbfc;
}

/* Search Stats */
.search-stats {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px 32px;
  margin-bottom: 32px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-keyword {
  font-size: 16px;
  font-weight: 600;
  color: var(--heading-color);
}

.search-keyword::before {
  content: "搜索关键词：";
  font-weight: 400;
  color: var(--muted-color);
}

.search-count {
  font-size: 14px;
  color: var(--muted-color);
  padding: 4px 12px;
  background: rgba(0, 119, 255, 0.08);
  border-radius: 20px;
}

.search-filters {
  display: flex;
  gap: 24px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: var(--muted-color);
  margin: 0;
  white-space: nowrap;
}

.filter-group .form-select {
  min-width: 120px;
  font-size: 14px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
}

/* Search Results List */
.search-results-list {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.search-result-item {
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background: rgba(0, 119, 255, 0.02);
  transform: translateX(2px);
}

.search-result-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--accent-color);
  transform: scaleY(0);
  transition: transform 0.2s ease;
}

.search-result-item:hover::before {
  transform: scaleY(1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.result-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--heading-color);
  margin: 0;
  line-height: 1.4;
}

.result-category {
  background: var(--accent-color);
  color: white;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.result-description {
  color: var(--muted-color);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 16px;
}

.result-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--muted-color);
}

.result-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.result-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.result-link {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color 0.2s ease;
}

.result-link:hover {
  color: var(--accent-hover);
}

/* No Results */
.no-results {
  text-align: center;
  padding: 80px 20px;
  color: var(--muted-color);
}

.no-results i {
  font-size: 64px;
  color: #e2e8f0;
  margin-bottom: 24px;
  display: block;
}

.no-results h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 12px;
}

.no-results p {
  font-size: 1rem;
  margin-bottom: 24px;
}

.no-results .suggestions {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.no-results .suggestions h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--heading-color);
  margin-bottom: 12px;
}

.no-results .suggestions ul {
  list-style: none;
  padding: 0;
}

.no-results .suggestions li {
  padding: 4px 0;
  color: var(--muted-color);
}

.no-results .suggestions li::before {
  content: "•";
  color: var(--accent-color);
  margin-right: 8px;
}

/* Search Pagination */
.search-pagination {
  margin-top: 40px;
}

.pagination .page-link {
  color: var(--muted-color);
  border: 1px solid var(--border-color);
  padding: 8px 16px;
}

.pagination .page-item.active .page-link {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.pagination .page-link:hover {
  color: var(--accent-color);
  background-color: rgba(0, 119, 255, 0.05);
  border-color: rgba(0, 119, 255, 0.2);
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .search-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .search-filters {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
    width: 100%;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-group .form-select {
    flex: 1;
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .search-hero {
    padding: 100px 0 30px 0;
  }
  
  .search-header h1 {
    font-size: 1.75rem;
  }
  
  .search-hero .search-box .form-control {
    height: 48px;
    font-size: 14px;
  }
  
  .search-hero .search-box .search-btn {
    width: 36px;
    height: 36px;
  }
  
  .search-results {
    padding: 40px 0;
  }
  
  .search-stats {
    padding: 20px;
  }
  
  .search-result-item {
    padding: 20px;
  }
  
  .result-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .result-meta {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
