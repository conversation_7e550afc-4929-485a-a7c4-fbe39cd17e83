/*--------------------------------------------------------------
# Help Search Results Page JavaScript
--------------------------------------------------------------*/

// 模拟文档数据 (与help.js中的数据保持一致)
const documentsData = {
  'getting-started': {
    title: '快速开始',
    description: '帮助您快速上手使用我们的产品',
    documents: [
      {
        id: 'quick-setup',
        title: '快速设置指南',
        description: '5分钟内完成基础配置，开始使用我们的服务',
        updateTime: '2024-01-15',
        icon: 'bi-play-circle'
      },
      {
        id: 'first-steps',
        title: '新手入门教程',
        description: '详细的步骤指导，帮助新用户快速熟悉产品功能',
        updateTime: '2024-01-10',
        icon: 'bi-book'
      }
    ]
  },
  'platform-guide': {
    title: '平台对接',
    description: '各大电商平台的对接指南',
    documents: [
      {
        id: 'platform-integration',
        title: '电商平台对接指南',
        description: '支持淘宝、京东、拼多多等主流电商平台的快速对接',
        updateTime: '2024-01-12',
        icon: 'bi-shop'
      }
    ]
  },
  'product-management': {
    title: '商品管理',
    description: '商品信息管理和同步',
    documents: [
      {
        id: 'product-sync',
        title: '商品信息同步',
        description: '实现多平台商品信息的统一管理和自动同步',
        updateTime: '2024-01-08',
        icon: 'bi-box'
      }
    ]
  },
  'order-management': {
    title: '订单管理',
    description: '订单处理和跟踪',
    documents: [
      {
        id: 'order-processing',
        title: '订单处理流程',
        description: '从订单接收到发货的完整处理流程说明',
        updateTime: '2024-01-05',
        icon: 'bi-receipt'
      }
    ]
  },
  'api-docs': {
    title: 'API文档',
    description: '开发者接口文档',
    documents: [
      {
        id: 'api-reference',
        title: 'API接口参考',
        description: '完整的API接口文档，包含请求参数和响应示例',
        updateTime: '2024-01-03',
        icon: 'bi-code-slash'
      }
    ]
  },
  'troubleshooting': {
    title: '故障排除',
    description: '常见问题解决方案',
    documents: [
      {
        id: 'common-issues',
        title: '常见问题解答',
        description: '收集用户最常遇到的问题及其解决方案',
        updateTime: '2024-01-01',
        icon: 'bi-tools'
      }
    ]
  }
};

// 全局变量
let currentSearchQuery = '';
let currentResults = [];
let currentPage = 1;
const resultsPerPage = 10;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
  // 初始化AOS动画
  AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    mirror: false
  });

  // 获取URL参数中的搜索关键词
  const urlParams = new URLSearchParams(window.location.search);
  const searchQuery = urlParams.get('q');
  
  if (searchQuery) {
    document.getElementById('searchInput').value = searchQuery;
    performSearch(searchQuery);
  }

  // 绑定事件
  bindEvents();
});

// 绑定事件
function bindEvents() {
  // 搜索框事件
  const searchInput = document.getElementById('searchInput');
  const searchBtn = document.querySelector('.search-btn');
  
  searchInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      performSearch(this.value.trim());
    }
  });
  
  searchBtn.addEventListener('click', function() {
    performSearch(searchInput.value.trim());
  });

  // 筛选和排序事件
  document.getElementById('sortBy').addEventListener('change', function() {
    sortResults(this.value);
  });
  
  document.getElementById('categoryFilter').addEventListener('change', function() {
    filterResults(this.value);
  });
}

// 执行搜索
function performSearch(query) {
  if (!query) {
    showNoResults('请输入搜索关键词');
    return;
  }

  currentSearchQuery = query;
  showLoading();
  
  // 模拟搜索延迟
  setTimeout(() => {
    const results = searchDocuments(query);
    currentResults = results;
    displayResults(results);
    updateSearchStats(query, results.length);
  }, 500);
}

// 搜索文档
function searchDocuments(query) {
  const results = [];
  const queryLower = query.toLowerCase();
  
  Object.keys(documentsData).forEach(categoryKey => {
    const category = documentsData[categoryKey];
    category.documents.forEach(doc => {
      const titleMatch = doc.title.toLowerCase().includes(queryLower);
      const descMatch = doc.description.toLowerCase().includes(queryLower);
      const categoryMatch = category.title.toLowerCase().includes(queryLower);
      
      if (titleMatch || descMatch || categoryMatch) {
        results.push({
          ...doc,
          category: categoryKey,
          categoryTitle: category.title,
          relevance: calculateRelevance(doc, category, queryLower)
        });
      }
    });
  });
  
  // 按相关性排序
  return results.sort((a, b) => b.relevance - a.relevance);
}

// 计算相关性得分
function calculateRelevance(doc, category, query) {
  let score = 0;
  const titleLower = doc.title.toLowerCase();
  const descLower = doc.description.toLowerCase();
  const categoryLower = category.title.toLowerCase();
  
  // 标题匹配得分最高
  if (titleLower.includes(query)) score += 10;
  if (titleLower.startsWith(query)) score += 5;
  
  // 描述匹配
  if (descLower.includes(query)) score += 5;
  
  // 分类匹配
  if (categoryLower.includes(query)) score += 3;
  
  return score;
}

// 显示搜索结果
function displayResults(results) {
  const resultsList = document.getElementById('searchResultsList');
  
  if (results.length === 0) {
    showNoResults();
    return;
  }
  
  const startIndex = (currentPage - 1) * resultsPerPage;
  const endIndex = startIndex + resultsPerPage;
  const pageResults = results.slice(startIndex, endIndex);
  
  resultsList.innerHTML = pageResults.map(result => `
    <div class="search-result-item" onclick="openDocument('${result.category}', '${result.id}')">
      <div class="result-header">
        <h3 class="result-title">${highlightText(result.title, currentSearchQuery)}</h3>
        <span class="result-category">${result.categoryTitle}</span>
      </div>
      <p class="result-description">${highlightText(result.description, currentSearchQuery)}</p>
      <div class="result-meta">
        <div class="result-date">
          <i class="bi bi-calendar3"></i>
          <span>更新于 ${result.updateTime}</span>
        </div>
        <div class="result-actions">
          <a href="#" class="result-link" onclick="event.stopPropagation(); openDocument('${result.category}', '${result.id}')">
            查看详情 <i class="bi bi-arrow-right"></i>
          </a>
        </div>
      </div>
    </div>
  `).join('');
  
  updatePagination(results.length);
}

// 高亮搜索关键词
function highlightText(text, query) {
  if (!query) return text;
  const regex = new RegExp(`(${query})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

// 显示加载状态
function showLoading() {
  const resultsList = document.getElementById('searchResultsList');
  resultsList.innerHTML = `
    <div class="search-loading">
      <div class="spinner-border" role="status">
        <span class="visually-hidden">搜索中...</span>
      </div>
      <p>正在搜索相关文档...</p>
    </div>
  `;
}

// 显示无结果
function showNoResults(message = '未找到相关文档') {
  const resultsList = document.getElementById('searchResultsList');
  resultsList.innerHTML = `
    <div class="no-results">
      <i class="bi bi-search"></i>
      <h3>未找到相关结果</h3>
      <p>${message}</p>
      <div class="suggestions">
        <h4>搜索建议：</h4>
        <ul>
          <li>检查关键词拼写是否正确</li>
          <li>尝试使用更通用的关键词</li>
          <li>减少关键词数量</li>
          <li>尝试使用同义词</li>
        </ul>
      </div>
    </div>
  `;
  
  // 隐藏分页
  document.querySelector('.search-pagination').style.display = 'none';
}

// 更新搜索统计信息
function updateSearchStats(query, count) {
  document.getElementById('searchKeyword').textContent = `"${query}"`;
  document.getElementById('searchCount').textContent = `找到 ${count} 个结果`;
}

// 排序结果
function sortResults(sortBy) {
  let sortedResults = [...currentResults];
  
  switch (sortBy) {
    case 'relevance':
      sortedResults.sort((a, b) => b.relevance - a.relevance);
      break;
    case 'date':
      sortedResults.sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime));
      break;
    case 'title':
      sortedResults.sort((a, b) => a.title.localeCompare(b.title));
      break;
  }
  
  currentResults = sortedResults;
  currentPage = 1;
  displayResults(sortedResults);
}

// 筛选结果
function filterResults(category) {
  let filteredResults = currentResults;
  
  if (category !== 'all') {
    filteredResults = currentResults.filter(result => result.category === category);
  }
  
  currentPage = 1;
  displayResults(filteredResults);
  updateSearchStats(currentSearchQuery, filteredResults.length);
}

// 更新分页
function updatePagination(totalResults) {
  const totalPages = Math.ceil(totalResults / resultsPerPage);
  const pagination = document.querySelector('.search-pagination');
  
  if (totalPages <= 1) {
    pagination.style.display = 'none';
    return;
  }
  
  pagination.style.display = 'block';
  // 这里可以添加更复杂的分页逻辑
}

// 打开文档详情
function openDocument(category, docId) {
  // 跳转到帮助页面的文档详情
  window.location.href = `help.html#${category}-${docId}`;
}

// 导出函数供全局使用
window.helpSearch = {
  performSearch,
  openDocument
};
