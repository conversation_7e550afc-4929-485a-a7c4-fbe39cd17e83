/**
 * Articles Page JavaScript
 */

// 文章数据
const articlesData = [
  {
    id: 1,
    title: "如何提高数字商品转化率：从流量到成交的完整指南",
    excerpt:
      "在数字商品销售中，转化率是衡量成功的关键指标。本文将分享提高转化率的实用技巧和策略，帮助您将更多访客转化为付费客户。通过优化产品页面、改善用户体验、实施A/B测试等方法，可以显著提升转化效果。",
    category: "operation",
    categoryName: "运营技巧",
    image: "https://via.placeholder.com/400x280",
    publishDate: "2024-01-15",
    readTime: 8,
    views: 1234,
    likes: 128,
    tags: ["转化率", "数字商品", "营销技巧"],
    featured: true,
    hasImage: true,
  },
  {
    id: 2,
    title: "电商平台对接完整指南：淘宝、京东、拼多多一站式接入",
    excerpt:
      "详细介绍如何将您的数字商品店铺与主流电商平台对接，实现多平台统一管理和自动发货。包含API配置、权限设置、数据同步等关键步骤。",
    category: "tutorial",
    categoryName: "使用教程",
    image: null,
    publishDate: "2024-01-12",
    readTime: 12,
    views: 2156,
    likes: 89,
    tags: ["平台对接", "淘宝", "京东", "拼多多"],
    featured: true,
    hasImage: false,
  },
  {
    id: 3,
    title: "自动发货系统优化技巧：提升效率降低成本",
    excerpt:
      "分享自动发货系统的优化方法，包括库存管理、订单处理、异常处理等方面的最佳实践。通过合理的系统配置和流程优化，可以大幅提升发货效率。",
    category: "tutorial",
    categoryName: "使用教程",
    image: "https://via.placeholder.com/400x280",
    publishDate: "2024-01-10",
    readTime: 6,
    views: 987,
    likes: 76,
    tags: ["自动发货", "系统优化", "效率提升"],
    featured: false,
    hasImage: true,
  },
  {
    id: 4,
    title: "2024年数字商品行业趋势分析报告",
    excerpt:
      "深入分析数字商品行业的发展趋势，包括市场规模、用户行为、技术发展等多个维度。报告显示，数字商品市场将继续保持高速增长。",
    category: "industry",
    categoryName: "行业资讯",
    image: "https://via.placeholder.com/400x280",
    publishDate: "2024-01-08",
    readTime: 15,
    views: 3421,
    likes: 234,
    tags: ["行业趋势", "市场分析", "数字商品"],
    featured: true,
    hasImage: true,
  },
  {
    id: 5,
    title: "成功案例：某游戏工作室月销售额突破100万的秘诀",
    excerpt:
      "分享一个游戏工作室如何通过法宝平台实现月销售额突破100万的成功经验和具体操作方法。从产品选择到营销推广的全流程解析。",
    category: "case",
    categoryName: "成功案例",
    image: null,
    publishDate: "2024-01-05",
    readTime: 10,
    views: 5678,
    likes: 456,
    tags: ["成功案例", "游戏", "销售技巧"],
    featured: false,
    hasImage: false,
  },
  {
    id: 6,
    title: "抖音小店数字商品运营策略全解析",
    excerpt:
      "详细介绍在抖音小店销售数字商品的运营策略，包括内容创作、直播带货、粉丝运营等。结合实际案例分析成功要素。",
    category: "operation",
    categoryName: "运营技巧",
    image: "https://via.placeholder.com/400x280",
    publishDate: "2024-01-03",
    readTime: 9,
    views: 2345,
    likes: 167,
    tags: ["抖音", "运营策略", "直播带货"],
    featured: false,
    hasImage: true,
  },
  {
    id: 7,
    title: "微信小程序商城开发实战指南",
    excerpt:
      "从零开始构建微信小程序商城，包括界面设计、支付集成、用户管理等核心功能的实现方法。",
    category: "technology",
    categoryName: "技术分享",
    image: null,
    publishDate: "2024-01-01",
    readTime: 20,
    views: 1876,
    likes: 145,
    tags: ["微信小程序", "商城开发", "技术实战"],
    featured: false,
    hasImage: false,
  },
  {
    id: 8,
    title: "数据驱动的电商运营决策分析",
    excerpt:
      "如何利用数据分析来指导电商运营决策，包括用户行为分析、销售数据挖掘、市场趋势预测等方面的实用方法。",
    category: "data",
    categoryName: "数据分析",
    image: "https://via.placeholder.com/400x280",
    publishDate: "2023-12-28",
    readTime: 14,
    views: 2987,
    likes: 203,
    tags: ["数据分析", "运营决策", "用户行为"],
    featured: false,
    hasImage: true,
  },
]

// 分页相关变量
let currentPage = 1
const articlesPerPage = 10

// 初始化
document.addEventListener("DOMContentLoaded", function () {
  // 初始化AOS动画
  AOS.init({
    duration: 800,
    easing: "ease-in-out",
    once: true,
    mirror: false,
  })

  // 加载文章列表
  loadArticles()
})

// 加载文章列表
function loadArticles() {
  // 分页处理
  const startIndex = (currentPage - 1) * articlesPerPage
  const endIndex = startIndex + articlesPerPage
  const paginatedArticles = articlesData.slice(startIndex, endIndex)

  displayArticles(paginatedArticles)
  updatePagination(articlesData.length)
  updateArticlesInfo()
}

// 显示文章列表
function displayArticles(articles) {
  const articlesGrid = document.getElementById("articlesGrid")

  if (articles.length === 0) {
    articlesGrid.innerHTML = `
      <div class="col-12 text-center">
        <div class="no-results">
          <i class="bi bi-search" style="font-size: 48px; color: #ccc; margin-bottom: 16px;"></i>
          <h4>未找到相关文章</h4>
          <p>请尝试其他关键词或浏览分类文章</p>
        </div>
      </div>
    `
    return
  }

  articlesGrid.innerHTML = articles
    .map(
      (article) => `
    <a href="article-detail.html?id=${article.id}" class="article-item ${
        article.hasImage ? "" : "no-image"
      }" data-aos="fade-up">
      ${
        article.hasImage
          ? `
        <div class="article-item-image">
          <img src="${article.image}" alt="${article.title}" />
          <div class="category-badge">${article.categoryName}</div>
        </div>
      `
          : ""
      }

      <div class="article-item-content">
        <div class="article-item-header">
          <h3 class="article-item-title">
            ${article.title}
          </h3>
          ${
            !article.hasImage
              ? `<div class="category-badge" style="position: static; margin-bottom: 8px; display: inline-block;">${article.categoryName}</div>`
              : ""
          }
        </div>

        <p class="article-item-excerpt">${article.excerpt}</p>

        <div class="article-item-footer">
          <div class="article-item-tags">
            ${article.tags
              .slice(0, 3)
              .map(
                (tag) => `
              <span class="article-item-tag">${tag}</span>
            `
              )
              .join("")}
          </div>
        </div>
      </div>
    </a>
  `
    )
    .join("")

  // 重新初始化AOS动画
  AOS.refresh()
}

// 更新分页
function updatePagination(totalArticles) {
  const totalPages = Math.ceil(totalArticles / articlesPerPage)
  const paginationContainer = document.getElementById("paginationContainer")

  if (totalPages <= 1) {
    paginationContainer.parentElement.style.display = "none"
    return
  }

  paginationContainer.parentElement.style.display = "block"

  let paginationHTML = ""

  // 上一页
  paginationHTML += `
    <li class="page-item ${currentPage === 1 ? "disabled" : ""}">
      <a class="page-link" href="#" onclick="changePage(${
        currentPage - 1
      })" aria-label="上一页">
        <i class="bi bi-chevron-left"></i>
        <span class="d-none d-sm-inline">上一页</span>
      </a>
    </li>
  `

  // 首页
  if (currentPage > 3) {
    paginationHTML += `
      <li class="page-item">
        <a class="page-link" href="#" onclick="changePage(1)">1</a>
      </li>
    `
    if (currentPage > 4) {
      paginationHTML += `
        <li class="page-item disabled">
          <span class="page-link">...</span>
        </li>
      `
    }
  }

  // 当前页附近的页码
  const startPage = Math.max(1, currentPage - 2)
  const endPage = Math.min(totalPages, currentPage + 2)

  for (let i = startPage; i <= endPage; i++) {
    paginationHTML += `
      <li class="page-item ${i === currentPage ? "active" : ""}">
        <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
      </li>
    `
  }

  // 尾页
  if (currentPage < totalPages - 2) {
    if (currentPage < totalPages - 3) {
      paginationHTML += `
        <li class="page-item disabled">
          <span class="page-link">...</span>
        </li>
      `
    }
    paginationHTML += `
      <li class="page-item">
        <a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a>
      </li>
    `
  }

  // 下一页
  paginationHTML += `
    <li class="page-item ${currentPage === totalPages ? "disabled" : ""}">
      <a class="page-link" href="#" onclick="changePage(${
        currentPage + 1
      })" aria-label="下一页">
        <span class="d-none d-sm-inline">下一页</span>
        <i class="bi bi-chevron-right"></i>
      </a>
    </li>
  `

  paginationContainer.innerHTML = paginationHTML
}

// 更新文章信息
function updateArticlesInfo() {
  const totalArticles = articlesData.length
  const totalPages = Math.ceil(totalArticles / articlesPerPage)

  document.getElementById("totalArticles").textContent = totalArticles
  document.getElementById("currentPageInfo").textContent = currentPage
  document.getElementById("totalPages").textContent = totalPages
}

// 切换页面
function changePage(page) {
  const totalPages = Math.ceil(articlesData.length / articlesPerPage)

  if (page < 1 || page > totalPages) return

  currentPage = page
  loadArticles()

  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: "smooth" })
}
