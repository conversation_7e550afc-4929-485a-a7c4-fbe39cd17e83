<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>产品生态 - 法宝</title>
    <meta
      name="description"
      content="法宝产品生态，支持淘宝、天猫、京东、拼多多、抖音、快手等主流电商平台的数字商品自动发货"
    />
    <meta
      name="keywords"
      content="法宝,产品生态,淘宝,京东,拼多多,抖音,快手,自动发货"
    />
    <!-- Favicons -->
    <link rel="shortcut icon" href="assets/favicon.ico" type="image/x-icon" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Vendor CSS Files -->
    <link
      href="assets/vendor/bootstrap/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="assets/vendor/bootstrap-icons/bootstrap-icons.css"
      rel="stylesheet"
    />
    <link href="assets/vendor/aos/aos.css" rel="stylesheet" />

    <!-- Main CSS File -->
    <link href="assets/css/main.css" rel="stylesheet" />
    <link href="assets/css/products.css" rel="stylesheet" />
  </head>

  <body class="products-page">
    <header id="header" class="header d-flex align-items-center fixed-top">
      <div class="container position-relative d-flex align-items-center">
        <a href="index.html" class="logo d-flex align-items-center">
          <svg viewBox="0 0 67.7 18.22">
            <g fill="#fff">
              <path
                d="M19.33 8.65c1.02-.91 1.67-2.29 1.64-3.83-.05-2.67-2.19-4.77-4.7-4.77h-5.78v1.64c0 .7.36 1.37.96 1.66.22.11.44.25.63.4.89.71 1.47 1.83 1.47 3.11 0 .49-.09.96-.25 1.39-.22.6-.57 1.13-1.02 1.54-.23.21-.48.39-.75.53 2.34.75 4.05 3.05 4.05 5.79v.2h8.03c.06-.36.09-.73.09-1.11 0-3.03-1.83-5.6-4.37-6.56M0 16.32h4.23v-.21c0-2.09 1-3.94 2.52-5.02.47-.33.98-.59 1.53-.77v-.79h-1c-.2-.23-.38-.48-.53-.76-.3-.57-.48-1.22-.48-1.92 0-1.54.85-2.87 2.08-3.5.62-.31.98-1 .98-1.73V.04H0v16.28zM0 17.04v1.18h4.58c-.13-.38-.23-.77-.29-1.18H0zM15.51 17.04c-.06.41-.16.81-.29 1.18h7.82c.17-.38.31-.77.42-1.18h-7.95z"
              />
            </g>
            <g fill="#fff">
              <path
                d="M29.08.92h4v2.24h-4zM65.45 1.61h-5.84V.16h-2.24v1.45h-5.82v-.45h-2.24v4.32h2.24V3.85h13.9v1.63h2.25V1.61h-2.25zM65.27 15.31h-5.66v-2.63h7.58v-1.84h-7.58V8.3h7.88V6.46h-18V8.3h7.88v2.54h-7.58v1.84h7.58v2.63h-7.88v2.24h18.03V14h-2.25v1.31zM29.08 7.58h4v2.24h-4zM29.08 15.03h4v2.24h-4zM44.14 11.88l.76 3.43h-7.13l1.3-5.89h8.41V7.17H42V3.91h5.24V1.66H42V0h-2.25v1.66H34.5v2.25h5.25v3.26h-5.48v2.25h2.46l-1.8 8.13H47.74l-1.26-5.67h-2.34z"
              />
            </g>
          </svg>
        </a>

        <div class="header-right d-flex align-items-center ms-auto">
          <nav id="navmenu" class="navmenu">
            <ul>
              <li><a href="index.html">首页</a></li>
              <li><a href="products.html" class="active">产品生态</a></li>
              <li><a href="help.html">使用帮助</a></li>
              <li><a href="articles.html">文章资讯</a></li>
              <li><a href="index.html#scene">系统对接</a></li>
              <li><a href="index.html#introduction">商务合作</a></li>
              <li><a href="index.html#faq">问题答疑</a></li>
            </ul>
            <button
              class="mobile-nav-toggle d-xl-none"
              aria-label="Toggle navigation"
            >
              <span></span>
              <span></span>
              <span></span>
            </button>
          </nav>

          <div class="header-social-links">
            <a class="btn-login" href="https://console.fabao.com">登录控制台</a>
          </div>
        </div>
      </div>
    </header>

    <main class="main">
      <!-- Hero Section -->
      <section id="products-hero" class="products-hero section">
        <div class="container">
          <div class="row justify-content-center">
            <div class="col-lg-8 text-center" data-aos="fade-up">
              <h1>产品生态</h1>
              <p>全平台覆盖，一站式数字商品自动发货解决方案</p>
              <div class="hero-stats">
                <div class="stat-item">
                  <div class="stat-number">10+</div>
                  <div class="stat-label">支持平台</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">100万+</div>
                  <div class="stat-label">服务商家</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">99.9%</div>
                  <div class="stat-label">发货成功率</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Platform Filter Section -->
      <section class="platform-filter-section">
        <div class="container">
          <div class="filter-tabs" data-aos="fade-up">
            <button class="filter-tab active" data-filter="all">
              <i class="bi bi-grid"></i>
              全部平台
            </button>
            <button class="filter-tab" data-filter="ecommerce">
              <i class="bi bi-shop"></i>
              电商平台
            </button>
            <button class="filter-tab" data-filter="social">
              <i class="bi bi-camera-video"></i>
              社交电商
            </button>
            <button class="filter-tab" data-filter="live">
              <i class="bi bi-broadcast"></i>
              直播带货
            </button>
          </div>
        </div>
      </section>

      <!-- Products Grid Section -->
      <section class="products-grid-section">
        <div class="container">
          <div class="products-grid" id="productsGrid">
            <!-- 淘宝 -->
            <div
              class="product-card show"
              data-category="ecommerce"
              data-aos="fade-up"
            >
              <div class="popular-badge">热门</div>

              <div class="product-header">
                <div class="product-logo taobao">淘</div>
                <div class="product-info">
                  <h3>淘宝</h3>
                  <div class="product-category">电商平台</div>
                </div>
              </div>

              <div class="product-description">
                国内最大的C2C电商平台，支持个人和企业店铺的数字商品自动发货
              </div>

              <div class="product-features">
                <h4>核心功能</h4>
                <div class="features-list">
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    自动发货
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    订单同步
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    库存管理
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    批量操作
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    数据统计
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    异常处理
                  </div>
                </div>
              </div>

              <div class="product-stats">
                <div class="stat">
                  <div class="stat-value">50万+</div>
                  <div class="stat-label">服务商家</div>
                </div>
                <div class="stat">
                  <div class="stat-value">1000万+</div>
                  <div class="stat-label">处理订单</div>
                </div>
                <div class="stat">
                  <div class="stat-value">99.8%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>

              <div class="product-actions">
                <a
                  href="help-detail.html?category=platform-guide&doc=taobao-integration"
                  class="action-btn primary"
                >
                  接入指南
                </a>
                <a
                  href="#"
                  class="action-btn secondary"
                  onclick="showProductDemo('taobao')"
                >
                  功能演示
                </a>
              </div>
            </div>

            <!-- 天猫 -->
            <div
              class="product-card show"
              data-category="ecommerce"
              data-aos="fade-up"
            >
              <div class="popular-badge">热门</div>

              <div class="product-header">
                <div class="product-logo tmall">天</div>
                <div class="product-info">
                  <h3>天猫</h3>
                  <div class="product-category">电商平台</div>
                </div>
              </div>

              <div class="product-description">
                阿里巴巴旗下B2C电商平台，为品牌商家提供专业的数字商品发货解决方案
              </div>

              <div class="product-features">
                <h4>核心功能</h4>
                <div class="features-list">
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    自动发货
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    订单同步
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    库存管理
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    批量操作
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    数据统计
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    品牌保护
                  </div>
                </div>
              </div>

              <div class="product-stats">
                <div class="stat">
                  <div class="stat-value">20万+</div>
                  <div class="stat-label">服务商家</div>
                </div>
                <div class="stat">
                  <div class="stat-value">800万+</div>
                  <div class="stat-label">处理订单</div>
                </div>
                <div class="stat">
                  <div class="stat-value">99.9%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>

              <div class="product-actions">
                <a
                  href="help-detail.html?category=platform-guide&doc=tmall-integration"
                  class="action-btn primary"
                >
                  接入指南
                </a>
                <a
                  href="#"
                  class="action-btn secondary"
                  onclick="showProductDemo('tmall')"
                >
                  功能演示
                </a>
              </div>
            </div>

            <!-- 京东 -->
            <div
              class="product-card show"
              data-category="ecommerce"
              data-aos="fade-up"
            >
              <div class="popular-badge">热门</div>

              <div class="product-header">
                <div class="product-logo jd">京</div>
                <div class="product-info">
                  <h3>京东</h3>
                  <div class="product-category">电商平台</div>
                </div>
              </div>

              <div class="product-description">
                中国领先的自营式电商平台，提供高品质的数字商品交付服务
              </div>

              <div class="product-features">
                <h4>核心功能</h4>
                <div class="features-list">
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    自动发货
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    订单同步
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    库存管理
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    批量操作
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    数据统计
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    质量保障
                  </div>
                </div>
              </div>

              <div class="product-stats">
                <div class="stat">
                  <div class="stat-value">15万+</div>
                  <div class="stat-label">服务商家</div>
                </div>
                <div class="stat">
                  <div class="stat-value">600万+</div>
                  <div class="stat-label">处理订单</div>
                </div>
                <div class="stat">
                  <div class="stat-value">99.7%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>

              <div class="product-actions">
                <a
                  href="help-detail.html?category=platform-guide&doc=jd-integration"
                  class="action-btn primary"
                >
                  接入指南
                </a>
                <a
                  href="#"
                  class="action-btn secondary"
                  onclick="showProductDemo('jd')"
                >
                  功能演示
                </a>
              </div>
            </div>

            <!-- 拼多多 -->
            <div
              class="product-card show"
              data-category="ecommerce"
              data-aos="fade-up"
            >
              <div class="popular-badge">热门</div>

              <div class="product-header">
                <div class="product-logo pdd">拼</div>
                <div class="product-info">
                  <h3>拼多多</h3>
                  <div class="product-category">电商平台</div>
                </div>
              </div>

              <div class="product-description">
                新兴社交电商平台，以团购模式为特色的数字商品销售渠道
              </div>

              <div class="product-features">
                <h4>核心功能</h4>
                <div class="features-list">
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    自动发货
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    订单同步
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    库存管理
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    批量操作
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    团购支持
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    社交分享
                  </div>
                </div>
              </div>

              <div class="product-stats">
                <div class="stat">
                  <div class="stat-value">30万+</div>
                  <div class="stat-label">服务商家</div>
                </div>
                <div class="stat">
                  <div class="stat-value">500万+</div>
                  <div class="stat-label">处理订单</div>
                </div>
                <div class="stat">
                  <div class="stat-value">99.5%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>

              <div class="product-actions">
                <a
                  href="help-detail.html?category=platform-guide&doc=pdd-integration"
                  class="action-btn primary"
                >
                  接入指南
                </a>
                <a
                  href="#"
                  class="action-btn secondary"
                  onclick="showProductDemo('pdd')"
                >
                  功能演示
                </a>
              </div>
            </div>

            <!-- 抖音 -->
            <div
              class="product-card show"
              data-category="social"
              data-aos="fade-up"
            >
              <div class="product-header">
                <div class="product-logo douyin">抖</div>
                <div class="product-info">
                  <h3>抖音</h3>
                  <div class="product-category">社交电商</div>
                </div>
              </div>

              <div class="product-description">
                短视频+电商的创新模式，通过直播和短视频销售数字商品
              </div>

              <div class="product-features">
                <h4>核心功能</h4>
                <div class="features-list">
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    自动发货
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    订单同步
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    直播带货
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    短视频营销
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    数据统计
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    粉丝管理
                  </div>
                </div>
              </div>

              <div class="product-stats">
                <div class="stat">
                  <div class="stat-value">25万+</div>
                  <div class="stat-label">服务商家</div>
                </div>
                <div class="stat">
                  <div class="stat-value">400万+</div>
                  <div class="stat-label">处理订单</div>
                </div>
                <div class="stat">
                  <div class="stat-value">99.3%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>

              <div class="product-actions">
                <a
                  href="help-detail.html?category=platform-guide&doc=douyin-integration"
                  class="action-btn primary"
                >
                  接入指南
                </a>
                <a
                  href="#"
                  class="action-btn secondary"
                  onclick="showProductDemo('douyin')"
                >
                  功能演示
                </a>
              </div>
            </div>

            <!-- 快手 -->
            <div
              class="product-card show"
              data-category="social"
              data-aos="fade-up"
            >
              <div class="product-header">
                <div class="product-logo kuaishou">快</div>
                <div class="product-info">
                  <h3>快手</h3>
                  <div class="product-category">社交电商</div>
                </div>
              </div>

              <div class="product-description">
                老铁经济的代表平台，通过直播和短视频实现数字商品销售
              </div>

              <div class="product-features">
                <h4>核心功能</h4>
                <div class="features-list">
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    自动发货
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    订单同步
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    直播带货
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    短视频营销
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    数据统计
                  </div>
                  <div class="feature-item">
                    <i class="bi bi-check-circle-fill"></i>
                    社区运营
                  </div>
                </div>
              </div>

              <div class="product-stats">
                <div class="stat">
                  <div class="stat-value">18万+</div>
                  <div class="stat-label">服务商家</div>
                </div>
                <div class="stat">
                  <div class="stat-value">300万+</div>
                  <div class="stat-label">处理订单</div>
                </div>
                <div class="stat">
                  <div class="stat-value">99.2%</div>
                  <div class="stat-label">成功率</div>
                </div>
              </div>

              <div class="product-actions">
                <a
                  href="help-detail.html?category=platform-guide&doc=kuaishou-integration"
                  class="action-btn primary"
                >
                  接入指南
                </a>
                <a
                  href="#"
                  class="action-btn secondary"
                  onclick="showProductDemo('kuaishou')"
                >
                  功能演示
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Features Comparison Section -->
      <section class="features-comparison-section">
        <div class="container">
          <div class="section-header" data-aos="fade-up">
            <h3>功能对比</h3>
            <p>不同平台支持的核心功能一览</p>
          </div>

          <div
            class="comparison-table-wrapper"
            data-aos="fade-up"
            data-aos-delay="100"
          >
            <table class="comparison-table">
              <thead>
                <tr>
                  <th>功能特性</th>
                  <th>淘宝/天猫</th>
                  <th>京东</th>
                  <th>拼多多</th>
                  <th>抖音</th>
                  <th>快手</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>自动发货</td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                </tr>
                <tr>
                  <td>订单同步</td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                </tr>
                <tr>
                  <td>库存管理</td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-x-circle-fill text-danger"></i></td>
                  <td><i class="bi bi-x-circle-fill text-danger"></i></td>
                </tr>
                <tr>
                  <td>批量操作</td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                </tr>
                <tr>
                  <td>数据统计</td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                  <td><i class="bi bi-check-circle-fill text-success"></i></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>

      <!-- Integration Guide Section -->
      <section class="integration-guide-section">
        <div class="container">
          <div class="section-header" data-aos="fade-up">
            <h3>快速接入</h3>
            <p>三步完成平台对接，开启自动化运营</p>
          </div>

          <div class="row">
            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="100">
              <div class="guide-step">
                <div class="step-number">1</div>
                <div class="step-icon">
                  <i class="bi bi-person-plus"></i>
                </div>
                <h4>注册授权</h4>
                <p>注册法宝账号，完成平台店铺授权，建立安全连接</p>
              </div>
            </div>

            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="200">
              <div class="guide-step">
                <div class="step-number">2</div>
                <div class="step-icon">
                  <i class="bi bi-box"></i>
                </div>
                <h4>商品配置</h4>
                <p>上传数字商品，设置发货规则，配置商品映射关系</p>
              </div>
            </div>

            <div class="col-lg-4" data-aos="fade-up" data-aos-delay="300">
              <div class="guide-step">
                <div class="step-number">3</div>
                <div class="step-icon">
                  <i class="bi bi-rocket-takeoff"></i>
                </div>
                <h4>开始运营</h4>
                <p>启动自动发货，实时监控订单，享受自动化运营</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer id="footer" class="footer dark-background">
      <div class="container">
        <div class="row gy-3">
          <div class="col-lg-3 col-md-6 d-flex">
            <i class="bi bi-geo-alt icon"></i>
            <div class="address">
              <h4>地址</h4>
              <p>中国 · 深圳</p>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 d-flex">
            <i class="bi bi-telephone icon"></i>
            <div class="address">
              <h4>联系方式</h4>
              <p>
                <strong>邮箱:</strong> <span><EMAIL></span><br />
                <strong>QQ群:</strong> <span>123456789</span>
              </p>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 d-flex">
            <i class="bi bi-clock icon"></i>
            <div class="address">
              <h4>服务时间</h4>
              <p>
                <strong>周一至周五:</strong> <span>9:00 - 18:00</span><br />
                <strong>周末:</strong> <span>在线支持</span>
              </p>
            </div>
          </div>

          <div class="col-lg-3 col-md-6">
            <h4>关注我们</h4>
            <div class="social-links d-flex">
              <a href="#" class="twitter"><i class="bi bi-twitter-x"></i></a>
              <a href="#" class="facebook"><i class="bi bi-facebook"></i></a>
              <a href="#" class="instagram"><i class="bi bi-instagram"></i></a>
              <a href="#" class="linkedin"><i class="bi bi-linkedin"></i></a>
            </div>
          </div>
        </div>
      </div>

      <div class="container copyright text-center mt-4">
        <p>
          © <span>Copyright</span> <strong class="px-1 sitename">法宝</strong>
          <span>All Rights Reserved</span>
        </p>
      </div>
    </footer>

    <!-- Scroll Top -->
    <a
      href="#"
      id="scroll-top"
      class="scroll-top d-flex align-items-center justify-content-center"
      ><i class="bi bi-arrow-up-short"></i
    ></a>

    <!-- Vendor JS Files -->
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendor/aos/aos.js"></script>

    <!-- Main JS File -->
    <script src="assets/js/main.js"></script>
    <script src="assets/js/products.js"></script>
  </body>
</html>
